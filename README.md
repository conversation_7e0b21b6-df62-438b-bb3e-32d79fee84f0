# OneClickAway Compose - Clean Architecture with ObjectBox & MVI

A complete Android application demonstrating modern Android development practices with **Clean Architecture**, **MVI (Model-View-Intent)** pattern, **ObjectBox** local database, and **Jetpack Compose**.

## 🏗️ Architecture Overview

This project follows **Clean Architecture** principles with three distinct modules:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Presentation  │    │     Domain      │    │      Data       │
│     (app)       │───▶│   (business)    │◀───│   (database)    │
│                 │    │                 │    │                 │
│ • UI (Compose)  │    │ • Use Cases     │    │ • Repository    │
│ • ViewModel     │    │ • Models        │    │ • ObjectBox     │
│ • MVI Pattern   │    │ • Interfaces    │    │ • Entities      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Why Clean Architecture?
- **Separation of Concerns**: Each layer has a single responsibility
- **Testability**: Easy to unit test each layer independently
- **Maintainability**: Changes in one layer don't affect others
- **Scalability**: Easy to add new features without breaking existing code

**Reference**: [Clean Architecture by Uncle Bob](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)

## 🎯 MVI Pattern Implementation

The app implements **Model-View-Intent** pattern for predictable state management:

```kotlin
// State - Represents UI state
sealed class NoteState {
    object Loading : NoteState()
    data class Success(val notes: List<Note>) : NoteState()
    data class Error(val message: String) : NoteState()
}

// Intent - Represents user actions
sealed class NoteIntent {
    object LoadNotes : NoteIntent()
    data class AddNote(val text: String) : NoteIntent()
    data class DeleteNote(val noteId: Long) : NoteIntent()
}
```

### Why MVI?
- **Unidirectional Data Flow**: Predictable state changes
- **Immutable State**: Prevents accidental mutations
- **Testability**: Easy to test state transitions
- **Debugging**: Clear action → state mapping

**Reference**: [MVI Pattern by Hannes Dorfmann](https://hannesdorfmann.com/android/model-view-intent/)

## 🗄️ ObjectBox Integration

**ObjectBox** provides high-performance local database with minimal setup:

```kotlin
@Entity
data class NoteEntity(
    @Id var id: Long = 0,
    var text: String = "",
    var createdAt: Long = 0
)
```

### Why ObjectBox?
- **Performance**: 10x faster than SQLite in many scenarios
- **Simplicity**: No SQL, minimal boilerplate
- **Type Safety**: Compile-time query validation
- **Reactive**: Built-in Flow support for reactive UI

**Reference**: [ObjectBox Documentation](https://docs.objectbox.io/android)

## 🛠️ Technology Stack

| Component | Library | Version | Purpose |
|-----------|---------|---------|---------|
| **UI Framework** | Jetpack Compose | Latest | Modern declarative UI |
| **Architecture** | Clean Architecture + MVI | - | Separation of concerns |
| **Database** | ObjectBox | 4.0.3 | High-performance local storage |
| **DI** | Hilt | 2.54 | Dependency injection |
| **Async** | Kotlin Coroutines | 1.10.1 | Asynchronous programming |
| **State** | StateFlow | - | Reactive state management |
| **Testing** | JUnit + MockK + Turbine | - | Comprehensive testing |

## 📁 Project Structure

```
OneClickAwayCompose/
├── app/                                    # Presentation Layer
│   ├── presentation/
│   │   ├── screen/NoteScreen.kt           # Compose UI
│   │   ├── viewmodel/NoteViewModel.kt     # MVI ViewModel
│   │   ├── state/NoteState.kt             # UI State
│   │   └── intent/NoteIntent.kt           # User Intents
│   └── OneClickAwayApplication.kt         # Hilt Application
├── domain/                                # Business Logic Layer
│   ├── model/Note.kt                      # Domain Models
│   ├── repository/NoteRepository.kt       # Repository Interface
│   └── usecase/                           # Business Use Cases
│       ├── GetAllNotesUseCase.kt
│       ├── AddNoteUseCase.kt
│       └── DeleteNoteUseCase.kt
└── data/                                  # Data Layer
    ├── local/
    │   ├── entity/NoteEntity.kt           # ObjectBox Entity
    │   └── database/ObjectBoxManager.kt   # Database Manager
    ├── repository/NoteRepositoryImpl.kt   # Repository Implementation
    ├── mapper/NoteMapper.kt               # Data ↔ Domain Mapping
    └── di/DataModule.kt                   # Hilt Module
```

## 🧪 Testing Strategy

The project includes comprehensive tests at all levels:

### Unit Tests
- **Domain Layer**: Use case business logic validation
- **Presentation Layer**: ViewModel state management testing
- **Data Layer**: Repository operations with mocked dependencies

### Integration Tests
- **Data Layer**: Real ObjectBox database operations
- **End-to-End**: Complete data flow validation

### UI Tests
- **Compose Testing**: User interaction and UI state verification
- **Accessibility**: Semantic testing for better UX

**Reference**: [Android Testing Guide](https://developer.android.com/training/testing)

## 🚀 Getting Started

### Prerequisites
- Android Studio Hedgehog or later
- JDK 11 or higher
- Android SDK 24+

### Setup
1. Clone the repository
2. Open in Android Studio
3. Sync Gradle dependencies
4. Run the app

### Running Tests
```bash
# Unit tests
./gradlew test

# Integration tests
./gradlew connectedAndroidTest

# All tests
./gradlew check
```

## 📚 Key Learning Resources

- [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [MVI Pattern](https://hannesdorfmann.com/android/model-view-intent/)
- [ObjectBox Android](https://docs.objectbox.io/android)
- [Jetpack Compose](https://developer.android.com/jetpack/compose)
- [Hilt Dependency Injection](https://dagger.dev/hilt/)
- [Kotlin Coroutines](https://kotlinlang.org/docs/coroutines-overview.html)
- [StateFlow & SharedFlow](https://developer.android.com/kotlin/flow/stateflow-and-sharedflow)

## 🎯 Features Demonstrated

- ✅ Multi-module Clean Architecture
- ✅ MVI pattern with StateFlow
- ✅ ObjectBox local database
- ✅ Jetpack Compose UI
- ✅ Hilt dependency injection
- ✅ Kotlin Coroutines
- ✅ Comprehensive testing
- ✅ Modern Android practices

## 📄 License

This project is for educational purposes and demonstrates modern Android development practices.
