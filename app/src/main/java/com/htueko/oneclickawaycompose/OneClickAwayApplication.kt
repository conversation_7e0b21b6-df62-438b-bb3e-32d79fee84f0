package com.htueko.oneclickawaycompose

import android.app.Application
import dagger.hilt.android.HiltAndroidApp

/**
 * Application class for OneClickAway app.
 * 
 * WHY: @HiltAndroidApp annotation triggers <PERSON><PERSON>'s code generation and
 * enables dependency injection throughout the application.
 * 
 * WHY: ObjectBox initialization is handled automatically through the
 * ObjectBoxManager singleton, which is lazily initialized when first accessed.
 * This improves app startup time compared to eager initialization.
 * 
 * References:
 * - Hilt Application: https://dagger.dev/hilt/application
 * - ObjectBox Setup: https://docs.objectbox.io/android/android-gradle-plugin
 */
@HiltAndroidApp
class OneClickAwayApplication : Application()
