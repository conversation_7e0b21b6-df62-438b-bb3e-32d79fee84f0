package com.htueko.oneclickawaycompose.presentation.intent

/**
 * Represents user intents/actions in the MVI pattern for Notes screen.
 * 
 * WHY: Sealed classes ensure all user actions are explicitly defined
 * and handled, making the application behavior predictable.
 * 
 * WHY: Intent-based architecture separates user actions from business
 * logic, making the code more testable and maintainable.
 * 
 * WHY: Each intent represents a single user action, following the
 * Single Responsibility Principle.
 * 
 * References:
 * - MVI Pattern: https://hannesdorfmann.com/android/model-view-intent/
 * - Sealed Classes: https://kotlinlang.org/docs/sealed-classes.html
 * - SOLID Principles: https://en.wikipedia.org/wiki/SOLID
 */
sealed class NoteIntent {
    
    /**
     * Intent to load all notes from the data source.
     */
    object LoadNotes : NoteIntent()
    
    /**
     * Intent to add a new note.
     * @param text The text content of the note to add
     */
    data class AddNote(val text: String) : NoteIntent()
    
    /**
     * Intent to delete a specific note.
     * @param noteId The ID of the note to delete
     */
    data class DeleteNote(val noteId: Long) : NoteIntent()
    
    /**
     * Intent to refresh the notes list.
     */
    object RefreshNotes : NoteIntent()
}
