package com.htueko.oneclickawaycompose.presentation.state

import com.htueko.oneclickawaycompose.domain.model.Note

/**
 * Represents the UI state for the Notes screen following MVI pattern.
 * 
 * WHY: Sealed classes provide type-safe state representation and ensure
 * all possible states are handled explicitly in the UI layer.
 * 
 * WHY: Immutable state objects prevent accidental mutations and make
 * state changes predictable and debuggable.
 * 
 * WHY: Separating loading, success, and error states provides clear
 * UI feedback for different application states.
 * 
 * References:
 * - MVI Pattern: https://hannesdorfmann.com/android/model-view-intent/
 * - Sealed Classes: https://kotlinlang.org/docs/sealed-classes.html
 * - State Management: https://developer.android.com/topic/architecture/ui-layer/stateholders
 */
sealed class NoteState {
    
    /**
     * Initial loading state when data is being fetched.
     */
    object Loading : NoteState()
    
    /**
     * Success state containing the list of notes.
     * @param notes List of notes to display
     * @param isRefreshing Whether a refresh operation is in progress
     */
    data class Success(
        val notes: List<Note> = emptyList(),
        val isRefreshing: Boolean = false
    ) : NoteState()
    
    /**
     * Error state when an operation fails.
     * @param message Error message to display to the user
     * @param notes Previously loaded notes (if any) to maintain UI state
     */
    data class Error(
        val message: String,
        val notes: List<Note> = emptyList()
    ) : NoteState()
}
