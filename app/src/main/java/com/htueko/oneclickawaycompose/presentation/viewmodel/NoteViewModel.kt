package com.htueko.oneclickawaycompose.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.htueko.oneclickawaycompose.domain.usecase.AddNoteUseCase
import com.htueko.oneclickawaycompose.domain.usecase.DeleteNoteUseCase
import com.htueko.oneclickawaycompose.domain.usecase.GetAllNotesUseCase
import com.htueko.oneclickawaycompose.presentation.intent.NoteIntent
import com.htueko.oneclickawaycompose.presentation.state.NoteState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel implementing MVI pattern for Notes screen.
 * 
 * WHY: ViewModel survives configuration changes and provides a stable
 * interface between the UI and business logic layers.
 * 
 * WHY: StateFlow provides a reactive state holder that emits updates
 * to observers and handles backpressure automatically.
 * 
 * WHY: Using use cases instead of repository directly follows Clean
 * Architecture principles and encapsulates business logic.
 * 
 * WHY: Intent-based approach makes user actions explicit and testable,
 * following the MVI pattern principles.
 * 
 * References:
 * - MVI Pattern: https://hannesdorfmann.com/android/model-view-intent/
 * - ViewModel: https://developer.android.com/topic/libraries/architecture/viewmodel
 * - StateFlow: https://developer.android.com/kotlin/flow/stateflow-and-sharedflow
 * - Clean Architecture: https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html
 */
@HiltViewModel
class NoteViewModel @Inject constructor(
    private val getAllNotesUseCase: GetAllNotesUseCase,
    private val addNoteUseCase: AddNoteUseCase,
    private val deleteNoteUseCase: DeleteNoteUseCase
) : ViewModel() {
    
    private val _state = MutableStateFlow<NoteState>(NoteState.Loading)
    val state: StateFlow<NoteState> = _state.asStateFlow()
    
    init {
        // WHY: Load notes immediately when ViewModel is created to provide
        // immediate feedback to the user
        handleIntent(NoteIntent.LoadNotes)
    }
    
    /**
     * Handles user intents and updates the state accordingly.
     * WHY: Single entry point for all user actions makes the ViewModel
     * behavior predictable and easier to test.
     */
    fun handleIntent(intent: NoteIntent) {
        when (intent) {
            is NoteIntent.LoadNotes -> loadNotes()
            is NoteIntent.AddNote -> addNote(intent.text)
            is NoteIntent.DeleteNote -> deleteNote(intent.noteId)
            is NoteIntent.RefreshNotes -> refreshNotes()
        }
    }
    
    private fun loadNotes() {
        viewModelScope.launch {
            _state.value = NoteState.Loading
            
            getAllNotesUseCase()
                .catch { exception ->
                    // WHY: Catch exceptions and convert to error state to prevent crashes
                    _state.value = NoteState.Error(
                        message = exception.message ?: "Unknown error occurred"
                    )
                }
                .collect { notes ->
                    _state.value = NoteState.Success(notes = notes)
                }
        }
    }
    
    private fun addNote(text: String) {
        viewModelScope.launch {
            addNoteUseCase(text)
                .onFailure { exception ->
                    // WHY: Show error state while preserving current notes
                    val currentNotes = (_state.value as? NoteState.Success)?.notes ?: emptyList()
                    _state.value = NoteState.Error(
                        message = exception.message ?: "Failed to add note",
                        notes = currentNotes
                    )
                }
            // WHY: No need to handle success case as Flow from getAllNotesUseCase
            // will automatically emit updated list
        }
    }
    
    private fun deleteNote(noteId: Long) {
        viewModelScope.launch {
            deleteNoteUseCase(noteId)
                .onFailure { exception ->
                    val currentNotes = (_state.value as? NoteState.Success)?.notes ?: emptyList()
                    _state.value = NoteState.Error(
                        message = exception.message ?: "Failed to delete note",
                        notes = currentNotes
                    )
                }
        }
    }
    
    private fun refreshNotes() {
        val currentState = _state.value
        if (currentState is NoteState.Success) {
            // WHY: Show refreshing indicator while maintaining current notes
            _state.value = currentState.copy(isRefreshing = true)
        }
        
        viewModelScope.launch {
            getAllNotesUseCase()
                .catch { exception ->
                    _state.value = NoteState.Error(
                        message = exception.message ?: "Failed to refresh notes"
                    )
                }
                .collect { notes ->
                    _state.value = NoteState.Success(
                        notes = notes,
                        isRefreshing = false
                    )
                }
        }
    }
}
