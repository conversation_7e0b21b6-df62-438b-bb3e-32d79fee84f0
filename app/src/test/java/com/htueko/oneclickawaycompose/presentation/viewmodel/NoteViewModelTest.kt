package com.htueko.oneclickawaycompose.presentation.viewmodel

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.htueko.oneclickawaycompose.domain.model.Note
import com.htueko.oneclickawaycompose.domain.usecase.AddNoteUseCase
import com.htueko.oneclickawaycompose.domain.usecase.DeleteNoteUseCase
import com.htueko.oneclickawaycompose.domain.usecase.GetAllNotesUseCase
import com.htueko.oneclickawaycompose.presentation.intent.NoteIntent
import com.htueko.oneclickawaycompose.presentation.state.NoteState
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Rule
import org.junit.Test

/**
 * Unit tests for NoteViewModel.
 * 
 * WHY: ViewModel testing ensures the presentation logic works correctly
 * and state transitions happen as expected in response to user intents.
 * 
 * WHY: Using TestDispatcher provides deterministic testing of coroutines
 * and allows us to control when coroutines execute.
 * 
 * WHY: InstantTaskExecutorRule ensures LiveData/StateFlow operations
 * execute synchronously during testing.
 * 
 * References:
 * - ViewModel Testing: https://developer.android.com/topic/libraries/architecture/viewmodel#testing
 * - Coroutine Testing: https://kotlinlang.org/docs/coroutines-testing.html
 * - StateFlow Testing: https://developer.android.com/kotlin/flow/test
 */
@OptIn(ExperimentalCoroutinesApi::class)
class NoteViewModelTest {
    
    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()
    
    private val testDispatcher = StandardTestDispatcher()
    
    private lateinit var getAllNotesUseCase: GetAllNotesUseCase
    private lateinit var addNoteUseCase: AddNoteUseCase
    private lateinit var deleteNoteUseCase: DeleteNoteUseCase
    private lateinit var viewModel: NoteViewModel
    
    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        
        getAllNotesUseCase = mockk()
        addNoteUseCase = mockk()
        deleteNoteUseCase = mockk()
        
        // WHY: Mock the initial load that happens in ViewModel init
        every { getAllNotesUseCase() } returns flowOf(emptyList())
    }
    
    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }
    
    @Test
    fun `initial state should be Loading then Success with empty list`() = runTest {
        // Given
        every { getAllNotesUseCase() } returns flowOf(emptyList())
        
        // When
        viewModel = NoteViewModel(getAllNotesUseCase, addNoteUseCase, deleteNoteUseCase)
        advanceUntilIdle()
        
        // Then
        val finalState = viewModel.state.value
        assertTrue(finalState is NoteState.Success)
        assertEquals(emptyList(), finalState.notes)
    }
    
    @Test
    fun `handleIntent LoadNotes should update state with notes from use case`() = runTest {
        // Given
        val expectedNotes = listOf(
            Note(id = 1, text = "Note 1", createdAt = 1000L),
            Note(id = 2, text = "Note 2", createdAt = 2000L)
        )
        every { getAllNotesUseCase() } returns flowOf(expectedNotes)
        viewModel = NoteViewModel(getAllNotesUseCase, addNoteUseCase, deleteNoteUseCase)
        
        // When
        viewModel.handleIntent(NoteIntent.LoadNotes)
        advanceUntilIdle()
        
        // Then
        val finalState = viewModel.state.value
        assertTrue(finalState is NoteState.Success)
        assertEquals(expectedNotes, finalState.notes)
    }
    
    @Test
    fun `handleIntent AddNote with valid text should call addNoteUseCase`() = runTest {
        // Given
        val noteText = "Test note"
        every { getAllNotesUseCase() } returns flowOf(emptyList())
        coEvery { addNoteUseCase(noteText) } returns Result.success(Unit)
        viewModel = NoteViewModel(getAllNotesUseCase, addNoteUseCase, deleteNoteUseCase)
        
        // When
        viewModel.handleIntent(NoteIntent.AddNote(noteText))
        advanceUntilIdle()
        
        // Then
        coVerify { addNoteUseCase(noteText) }
    }
    
    @Test
    fun `handleIntent AddNote failure should update state to Error`() = runTest {
        // Given
        val noteText = "Test note"
        val errorMessage = "Failed to add note"
        every { getAllNotesUseCase() } returns flowOf(emptyList())
        coEvery { addNoteUseCase(noteText) } returns Result.failure(RuntimeException(errorMessage))
        viewModel = NoteViewModel(getAllNotesUseCase, addNoteUseCase, deleteNoteUseCase)
        advanceUntilIdle() // Let initial load complete
        
        // When
        viewModel.handleIntent(NoteIntent.AddNote(noteText))
        advanceUntilIdle()
        
        // Then
        val finalState = viewModel.state.value
        assertTrue(finalState is NoteState.Error)
        assertEquals(errorMessage, finalState.message)
    }
    
    @Test
    fun `handleIntent DeleteNote should call deleteNoteUseCase`() = runTest {
        // Given
        val noteId = 1L
        every { getAllNotesUseCase() } returns flowOf(emptyList())
        coEvery { deleteNoteUseCase(noteId) } returns Result.success(Unit)
        viewModel = NoteViewModel(getAllNotesUseCase, addNoteUseCase, deleteNoteUseCase)
        
        // When
        viewModel.handleIntent(NoteIntent.DeleteNote(noteId))
        advanceUntilIdle()
        
        // Then
        coVerify { deleteNoteUseCase(noteId) }
    }
    
    @Test
    fun `handleIntent DeleteNote failure should update state to Error`() = runTest {
        // Given
        val noteId = 1L
        val errorMessage = "Failed to delete note"
        every { getAllNotesUseCase() } returns flowOf(emptyList())
        coEvery { deleteNoteUseCase(noteId) } returns Result.failure(RuntimeException(errorMessage))
        viewModel = NoteViewModel(getAllNotesUseCase, addNoteUseCase, deleteNoteUseCase)
        advanceUntilIdle() // Let initial load complete
        
        // When
        viewModel.handleIntent(NoteIntent.DeleteNote(noteId))
        advanceUntilIdle()
        
        // Then
        val finalState = viewModel.state.value
        assertTrue(finalState is NoteState.Error)
        assertEquals(errorMessage, finalState.message)
    }
}
