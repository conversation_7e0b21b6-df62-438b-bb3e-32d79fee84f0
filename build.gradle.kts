// Top-level build file where you can add configuration options common to all sub-projects/modules.
// WHY: ObjectBox plugin must be applied at the root level to enable code generation across modules
// Reference: https://docs.objectbox.io/android/android-gradle-plugin
buildscript {
    repositories {
        google()
        mavenCentral()
        // WHY: ObjectBox plugin is hosted on their own repository
        maven { url = uri("https://objectbox.net/beta-repo/") }
    }
    dependencies {
        classpath("io.objectbox:objectbox-gradle-plugin:4.3.0")
    }
}

plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.android.library) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.kotlin.compose) apply false
    alias(libs.plugins.kotlin.kapt) apply false
    alias(libs.plugins.hilt.android) apply false
}