plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.kapt)
    alias(libs.plugins.hilt.android)
    id("io.objectbox")
}

android {
    namespace = "com.htueko.oneclickawaycompose.data"
    compileSdk = 36

    defaultConfig {
        minSdk = 24
        
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    
    kotlinOptions {
        jvmTarget = "11"
    }
}

dependencies {
    // Domain module dependency
    implementation(project(":domain"))
    
    // WHY: ObjectBox provides high-performance local database with minimal setup
    // Reference: https://docs.objectbox.io/android
    implementation(libs.objectbox.android)
    implementation(libs.objectbox.kotlin)
    
    // WHY: Hilt provides compile-time dependency injection
    // Reference: https://dagger.dev/hilt/
    implementation(libs.hilt.android)
    kapt(libs.hilt.compiler)
    
    // WHY: Coroutines enable asynchronous database operations
    // Reference: https://kotlinlang.org/docs/coroutines-overview.html
    implementation(libs.kotlinx.coroutines.android)
    
    // Testing dependencies
    testImplementation(libs.junit)
    testImplementation(libs.kotlinx.coroutines.test)
    testImplementation(libs.mockk)
    testImplementation(libs.turbine)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}
