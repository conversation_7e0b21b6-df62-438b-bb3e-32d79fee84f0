package com.htueko.oneclickawaycompose.data.local.database;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ObjectBoxManager_Factory implements Factory<ObjectBoxManager> {
  private final Provider<Context> contextProvider;

  public ObjectBoxManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public ObjectBoxManager get() {
    return newInstance(contextProvider.get());
  }

  public static ObjectBoxManager_Factory create(javax.inject.Provider<Context> contextProvider) {
    return new ObjectBoxManager_Factory(Providers.asDaggerProvider(contextProvider));
  }

  public static ObjectBoxManager_Factory create(Provider<Context> contextProvider) {
    return new ObjectBoxManager_Factory(contextProvider);
  }

  public static ObjectBoxManager newInstance(Context context) {
    return new ObjectBoxManager(context);
  }
}
