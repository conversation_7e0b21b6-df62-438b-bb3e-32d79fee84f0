
package com.htueko.oneclickawaycompose.data.local.entity;

import io.objectbox.BoxStore;
import io.objectbox.BoxStoreBuilder;
import io.objectbox.ModelBuilder;
import io.objectbox.ModelBuilder.EntityBuilder;
import io.objectbox.model.PropertyFlags;
import io.objectbox.model.PropertyType;

// THIS CODE IS GENERATED BY ObjectBox, DO NOT EDIT.
/**
 * Starting point for working with your ObjectBox. All boxes are set up for your objects here.
 * <p>
 * First steps (Android): get a builder using {@link #builder()}, call {@link BoxStoreBuilder#androidContext(Object)},
 * and {@link BoxStoreBuilder#build()} to get a {@link BoxStore} to work with.
 */
public class MyObjectBox {

    public static BoxStoreBuilder builder() {
        BoxStoreBuilder builder = new BoxStoreBuilder(getModel());
        builder.entity(NoteEntity_.__INSTANCE);
        return builder;
    }

    private static byte[] getModel() {
        ModelBuilder modelBuilder = new ModelBuilder();
        modelBuilder.lastEntityId(1, 6905062157516471904L);
        modelBuilder.lastIndexId(0, 0L);
        modelBuilder.lastRelationId(0, 0L);

        buildEntityNoteEntity(modelBuilder);

        return modelBuilder.build();
    }

    private static void buildEntityNoteEntity(ModelBuilder modelBuilder) {
        EntityBuilder entityBuilder = modelBuilder.entity("NoteEntity");
        entityBuilder.id(1, 6905062157516471904L).lastPropertyId(3, 8630346983247306173L);

        entityBuilder.property("id", PropertyType.Long).id(1, 4500914542639029033L)
                .flags(PropertyFlags.ID);
        entityBuilder.property("text", PropertyType.String).id(2, 3549254226700104438L);
        entityBuilder.property("createdAt", PropertyType.Long).id(3, 8630346983247306173L);


        entityBuilder.entityDone();
    }


}
