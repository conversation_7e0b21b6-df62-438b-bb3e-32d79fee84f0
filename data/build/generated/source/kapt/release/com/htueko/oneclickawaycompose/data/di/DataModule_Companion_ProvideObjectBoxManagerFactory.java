package com.htueko.oneclickawaycompose.data.di;

import android.content.Context;
import com.htueko.oneclickawaycompose.data.local.database.ObjectBoxManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DataModule_Companion_ProvideObjectBoxManagerFactory implements Factory<ObjectBoxManager> {
  private final Provider<Context> contextProvider;

  public DataModule_Companion_ProvideObjectBoxManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public ObjectBoxManager get() {
    return provideObjectBoxManager(contextProvider.get());
  }

  public static DataModule_Companion_ProvideObjectBoxManagerFactory create(
      javax.inject.Provider<Context> contextProvider) {
    return new DataModule_Companion_ProvideObjectBoxManagerFactory(Providers.asDaggerProvider(contextProvider));
  }

  public static DataModule_Companion_ProvideObjectBoxManagerFactory create(
      Provider<Context> contextProvider) {
    return new DataModule_Companion_ProvideObjectBoxManagerFactory(contextProvider);
  }

  public static ObjectBoxManager provideObjectBoxManager(Context context) {
    return Preconditions.checkNotNullFromProvides(DataModule.Companion.provideObjectBoxManager(context));
  }
}
