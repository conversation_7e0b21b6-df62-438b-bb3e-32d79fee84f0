package com.htueko.oneclickawaycompose.data.local.entity;

import io.objectbox.BoxStore;
import io.objectbox.Cursor;
import io.objectbox.annotation.apihint.Internal;
import io.objectbox.internal.CursorFactory;

// THIS CODE IS GENERATED BY ObjectBox, DO NOT EDIT.

/**
 * ObjectBox generated Cursor implementation for "NoteEntity".
 * Note that this is a low-level class: usually you should stick to the Box class.
 */
public final class NoteEntityCursor extends Cursor<NoteEntity> {
    @Internal
    static final class Factory implements CursorFactory<NoteEntity> {
        @Override
        public Cursor<NoteEntity> createCursor(io.objectbox.Transaction tx, long cursorHandle, BoxStore boxStoreForEntities) {
            return new NoteEntityCursor(tx, cursorHandle, boxStoreForEntities);
        }
    }

    private static final NoteEntity_.NoteEntityIdGetter ID_GETTER = NoteEntity_.__ID_GETTER;


    private final static int __ID_text = NoteEntity_.text.id;
    private final static int __ID_createdAt = NoteEntity_.createdAt.id;

    public NoteEntityCursor(io.objectbox.Transaction tx, long cursor, BoxStore boxStore) {
        super(tx, cursor, NoteEntity_.__INSTANCE, boxStore);
    }

    @Override
    public long getId(NoteEntity entity) {
        return ID_GETTER.getId(entity);
    }

    /**
     * Puts an object into its box.
     *
     * @return The ID of the object within its box.
     */
    @SuppressWarnings({"rawtypes", "unchecked"}) 
    @Override
    public long put(NoteEntity entity) {
        String text = entity.getText();
        int __id1 = text != null ? __ID_text : 0;

        long __assignedId = collect313311(cursor, entity.getId(), PUT_FLAG_FIRST | PUT_FLAG_COMPLETE,
                __id1, text, 0, null,
                0, null, 0, null,
                __ID_createdAt, entity.getCreatedAt(), 0, 0,
                0, 0, 0, 0,
                0, 0, 0, 0,
                0, 0, 0, 0);

        entity.setId(__assignedId);

        return __assignedId;
    }

}
