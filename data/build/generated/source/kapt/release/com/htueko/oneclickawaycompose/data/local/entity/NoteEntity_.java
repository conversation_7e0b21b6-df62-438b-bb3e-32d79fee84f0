
package com.htueko.oneclickawaycompose.data.local.entity;

import com.htueko.oneclickawaycompose.data.local.entity.NoteEntityCursor.Factory;
import io.objectbox.EntityInfo;
import io.objectbox.annotation.apihint.Internal;
import io.objectbox.internal.CursorFactory;
import io.objectbox.internal.IdGetter;

// THIS CODE IS GENERATED BY ObjectBox, DO NOT EDIT.

/**
 * Properties for entity "NoteEntity". Can be used for QueryBuilder and for referencing DB names.
 */
public final class NoteEntity_ implements EntityInfo<NoteEntity> {

    // Leading underscores for static constants to avoid naming conflicts with property names

    public static final String __ENTITY_NAME = "NoteEntity";

    public static final int __ENTITY_ID = 1;

    public static final Class<NoteEntity> __ENTITY_CLASS = NoteEntity.class;

    public static final String __DB_NAME = "NoteEntity";

    public static final CursorFactory<NoteEntity> __CURSOR_FACTORY = new Factory();

    @Internal
    static final NoteEntityIdGetter __ID_GETTER = new NoteEntityIdGetter();

    public final static NoteEntity_ __INSTANCE = new NoteEntity_();

    public final static io.objectbox.Property<NoteEntity> id =
        new io.objectbox.Property<>(__INSTANCE, 0, 1, long.class, "id", true, "id");

    public final static io.objectbox.Property<NoteEntity> text =
        new io.objectbox.Property<>(__INSTANCE, 1, 2, String.class, "text");

    public final static io.objectbox.Property<NoteEntity> createdAt =
        new io.objectbox.Property<>(__INSTANCE, 2, 3, long.class, "createdAt");

    @SuppressWarnings("unchecked")
    public final static io.objectbox.Property<NoteEntity>[] __ALL_PROPERTIES = new io.objectbox.Property[]{
        id,
        text,
        createdAt
    };

    public final static io.objectbox.Property<NoteEntity> __ID_PROPERTY = id;

    @Override
    public String getEntityName() {
        return __ENTITY_NAME;
    }

    @Override
    public int getEntityId() {
        return __ENTITY_ID;
    }

    @Override
    public Class<NoteEntity> getEntityClass() {
        return __ENTITY_CLASS;
    }

    @Override
    public String getDbName() {
        return __DB_NAME;
    }

    @Override
    public io.objectbox.Property<NoteEntity>[] getAllProperties() {
        return __ALL_PROPERTIES;
    }

    @Override
    public io.objectbox.Property<NoteEntity> getIdProperty() {
        return __ID_PROPERTY;
    }

    @Override
    public IdGetter<NoteEntity> getIdGetter() {
        return __ID_GETTER;
    }

    @Override
    public CursorFactory<NoteEntity> getCursorFactory() {
        return __CURSOR_FACTORY;
    }

    @Internal
    static final class NoteEntityIdGetter implements IdGetter<NoteEntity> {
        @Override
        public long getId(NoteEntity object) {
            return object.getId();
        }
    }

}
