package com.htueko.oneclickawaycompose.data.repository;

import com.htueko.oneclickawaycompose.data.local.database.ObjectBoxManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.Providers;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class NoteRepositoryImpl_Factory implements Factory<NoteRepositoryImpl> {
  private final Provider<ObjectBoxManager> objectBoxManagerProvider;

  public NoteRepositoryImpl_Factory(Provider<ObjectBoxManager> objectBoxManagerProvider) {
    this.objectBoxManagerProvider = objectBoxManagerProvider;
  }

  @Override
  public NoteRepositoryImpl get() {
    return newInstance(objectBoxManagerProvider.get());
  }

  public static NoteRepositoryImpl_Factory create(
      javax.inject.Provider<ObjectBoxManager> objectBoxManagerProvider) {
    return new NoteRepositoryImpl_Factory(Providers.asDaggerProvider(objectBoxManagerProvider));
  }

  public static NoteRepositoryImpl_Factory create(
      Provider<ObjectBoxManager> objectBoxManagerProvider) {
    return new NoteRepositoryImpl_Factory(objectBoxManagerProvider);
  }

  public static NoteRepositoryImpl newInstance(ObjectBoxManager objectBoxManager) {
    return new NoteRepositoryImpl(objectBoxManager);
  }
}
