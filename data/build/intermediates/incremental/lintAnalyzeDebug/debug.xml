<variant
    name="debug"
    package="com.htueko.oneclickawaycompose.data"
    minSdkVersion="24"
    targetSdkVersion="24"
    debuggable="true"
    mergedManifest="build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml"
    proguardFiles="build/intermediates/default_proguard_files/global/proguard-android.txt-8.12.0"
    consumerProguardFiles="consumer-rules.pro"
    partialResultsDir="build/intermediates/lint_partial_results/debug/lintAnalyzeDebug/out"
    desugaredMethodsFiles="/home/<USER>/.gradle/caches/8.13/transforms/04d2e936ccda1075a1edc168ec12bb89/transformed/D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src/main/AndroidManifest.xml"
        javaDirectories="src/main/java:src/debug/java:src/main/kotlin:src/debug/kotlin"
        resDirectories="src/main/res:src/debug/res"
        assetsDirectories="src/main/assets:src/debug/assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build/intermediates/javac/debug/compileDebugJavaWithJavac/classes:build/tmp/kotlin-classes/debug:build/tmp/kapt3/classes/debug:build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar"
      type="MAIN"
      applicationId="com.htueko.oneclickawaycompose.data"
      generatedSourceFolders="build/generated/source/kapt/debug:build/generated/ap_generated_sources/debug/out"
      generatedResourceFolders="build/generated/res/resValues/debug"
      desugaredMethodsFiles="/home/<USER>/.gradle/caches/8.13/transforms/04d2e936ccda1075a1edc168ec12bb89/transformed/D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
