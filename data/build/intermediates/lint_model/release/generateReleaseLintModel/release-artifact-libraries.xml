<libraries>
  <library
      name="io.objectbox:objectbox-kotlin:4.0.3@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/io.objectbox/objectbox-kotlin/4.0.3/f51bb624a875c0fbac6928d67cd8b23470dc0dd4/objectbox-kotlin-4.0.3.jar"
      resolved="io.objectbox:objectbox-kotlin:4.0.3"/>
  <library
      name="io.objectbox:objectbox-java:4.0.3@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/io.objectbox/objectbox-java/4.0.3/cee5cfa4e12246a787f5a18caa9f2c7d05e92c58/objectbox-java-4.0.3.jar"
      resolved="io.objectbox:objectbox-java:4.0.3"/>
  <library
      name="io.objectbox:objectbox-android:4.0.3@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/7eff198e1c39dab505c1a30087a05855/transformed/objectbox-android-4.0.3/jars/classes.jar"
      resolved="io.objectbox:objectbox-android:4.0.3"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/7eff198e1c39dab505c1a30087a05855/transformed/objectbox-android-4.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:domain::release"
      project=":domain"/>
  <library
      name="com.google.dagger:hilt-android:2.54@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/2702983501a53f7c230bf26be9c268fa/transformed/hilt-android-2.54/jars/classes.jar"
      resolved="com.google.dagger:hilt-android:2.54"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/2702983501a53f7c230bf26be9c268fa/transformed/hilt-android-2.54"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.5.1@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/3f662f34ce46601d59f8125b7629f881/transformed/fragment-1.5.1/jars/classes.jar"
      resolved="androidx.fragment:fragment:1.5.1"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/3f662f34ce46601d59f8125b7629f881/transformed/fragment-1.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.5.1@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/2b021cf59ac32486003571b26a8d45e5/transformed/activity-1.5.1/jars/classes.jar"
      resolved="androidx.activity:activity:1.5.1"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/2b021cf59ac32486003571b26a8d45e5/transformed/activity-1.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/83c5ca48af4eea9434869b6f299f82fe/transformed/lifecycle-viewmodel-savedstate-2.5.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/83c5ca48af4eea9434869b6f299f82fe/transformed/lifecycle-viewmodel-savedstate-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-core-jvm/1.10.1/fe066928754beda3d59c8282e04289546465a360/kotlinx-coroutines-core-jvm-1.10.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-android/1.10.1/864006e2e42ed4d76150651c635d038a060c20a2/kotlinx-coroutines-android-1.10.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1"/>
  <library
      name="androidx.core:core-ktx:1.2.0@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/fd7235d5feb845248876f2f64f45c738/transformed/core-ktx-1.2.0/jars/classes.jar"
      resolved="androidx.core:core-ktx:1.2.0"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/fd7235d5feb845248876f2f64f45c738/transformed/core-ktx-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/603391fb007128fb754285d1205874ec/transformed/loader-1.0.0/jars/classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/603391fb007128fb754285d1205874ec/transformed/loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/4946e087a3e4078d8556c70e1b89bd1b/transformed/viewpager-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/4946e087a3e4078d8556c70e1b89bd1b/transformed/viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/c8865107f78bd3c9f06ce5f51d84cb9f/transformed/customview-1.0.0/jars/classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/c8865107f78bd3c9f06ce5f51d84cb9f/transformed/customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.8.0@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/jars/classes.jar"
      resolved="androidx.core:core:1.8.0"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.3.1@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/e93bc51223aaf181fa7cf4c30c301d49/transformed/annotation-experimental-1.3.1/jars/classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.3.1"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/e93bc51223aaf181fa7cf4c30c301d49/transformed/annotation-experimental-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/862bb3137af1ebbe770e259225cdba37/transformed/lifecycle-viewmodel-2.5.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.5.1"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/862bb3137af1ebbe770e259225cdba37/transformed/lifecycle-viewmodel-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.0@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/d1f701969a94f6b2a3933c151b58d553/transformed/savedstate-1.2.0/jars/classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.0"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/d1f701969a94f6b2a3933c151b58d553/transformed/savedstate-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.2.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/2.2.0/fdfc65fbc42fda253a26f61dac3c0aca335fae96/kotlin-stdlib-2.2.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.2.0"/>
  <library
      name="io.objectbox:objectbox-java-api:4.0.3@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/io.objectbox/objectbox-java-api/4.0.3/b38b63074ada442c5afe501756d3be91fd3109c7/objectbox-java-api-4.0.3.jar"
      resolved="io.objectbox:objectbox-java-api:4.0.3"/>
  <library
      name="com.google.dagger:hilt-core:2.54@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.dagger/hilt-core/2.54/ed8168b208842a41252d2daefa97a9706a64c89/hilt-core-2.54.jar"
      resolved="com.google.dagger:hilt-core:2.54"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.code.findbugs/jsr305/3.0.2/25ea2e8b0c338a877313bd4672d3fe056ea78f0d/jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/23.0.0/8cc20c07506ec18e0834947b84a864bfc094484e/annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.dagger:dagger:2.54@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.dagger/dagger/2.54/9fe782cc9ef11a393cbf3e9399cf2b4b712fb716/dagger-2.54.jar"
      resolved="com.google.dagger:dagger:2.54"/>
  <library
      name="jakarta.inject:jakarta.inject-api:2.0.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/jakarta.inject/jakarta.inject-api/2.0.1/4c28afe1991a941d7702fe1362c365f0a8641d1e/jakarta.inject-api-2.0.1.jar"
      resolved="jakarta.inject:jakarta.inject-api:2.0.1"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/javax.inject/javax.inject/1/6975da39a7040257bd51d21a231b76c915872d38/javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="org.jspecify:jspecify:1.0.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.jspecify/jspecify/1.0.0/7425a601c1c7ec76645a78d22b8c6a627edee507/jspecify-1.0.0.jar"
      resolved="org.jspecify:jspecify:1.0.0"/>
  <library
      name="com.google.dagger:dagger-lint-aar:2.54@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/6c749d81e4b2d0eac0c9404a753c55b7/transformed/dagger-lint-aar-2.54/jars/classes.jar"
      resolved="com.google.dagger:dagger-lint-aar:2.54"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/6c749d81e4b2d0eac0c9404a753c55b7/transformed/dagger-lint-aar-2.54"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.5.1@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/1da5024a30b4b02bc362cb9234b7be8b/transformed/lifecycle-runtime-2.5.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.5.1"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/1da5024a30b4b02bc362cb9234b7be8b/transformed/lifecycle-runtime-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/59207bbff35cef087dc8fde755c6d790/transformed/lifecycle-livedata-2.0.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.0.0"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/59207bbff35cef087dc8fde755c6d790/transformed/lifecycle-livedata-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/bf78256ef2c7efca284702f1254a90e0/transformed/lifecycle-livedata-core-2.5.1/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.5.1"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/bf78256ef2c7efca284702f1254a90e0/transformed/lifecycle-livedata-core-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.5.1@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common/2.5.1/1fdb7349701e9cf2f0a69fc10642b6fef6bb3e12/lifecycle-common-2.5.1.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.5.1"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/ecc14256260940e6f839af7ad5e1d1e2/transformed/versionedparcelable-1.1.1/jars/classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/ecc14256260940e6f839af7ad5e1d1e2/transformed/versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection/1.1.0/1f27220b47669781457de0d600849a5de0e89909/collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.1.0@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/18f546b85ab08b619de719ae1272af29/transformed/core-runtime-2.1.0/jars/classes.jar"
      resolved="androidx.arch.core:core-runtime:2.1.0"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/18f546b85ab08b619de719ae1272af29/transformed/core-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.1.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.1.0/b3152fc64428c9354344bd89848ecddc09b6f07e/core-common-2.1.0.jar"
      resolved="androidx.arch.core:core-common:2.1.0"/>
  <library
      name="androidx.annotation:annotation:1.3.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation/1.3.0/21f49f5f9b85fc49de712539f79123119740595/annotation-1.3.0.jar"
      resolved="androidx.annotation:annotation:1.3.0"/>
  <library
      name="org.greenrobot:essentials:3.1.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/org.greenrobot/essentials/3.1.0/3bd606485d1626d3e61efa265e53235cfc47a8b6/essentials-3.1.0.jar"
      resolved="org.greenrobot:essentials:3.1.0"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="/home/<USER>/.gradle/caches/8.13/transforms/66613b599e1a35c2be0025ba596d0e26/transformed/tracing-1.0.0/jars/classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="/home/<USER>/.gradle/caches/8.13/transforms/66613b599e1a35c2be0025ba596d0e26/transformed/tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.0.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/androidx.concurrent/concurrent-futures/1.0.0/c1e77e3ee6f4643b77496a1ddf7a2eef1aefdaa1/concurrent-futures-1.0.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.0.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="/home/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/listenablefuture/1.0/c949a840a6acbc5268d088e47b04177bf90b3cad/listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
</libraries>
