<lint-module
    format="1"
    dir="/home/<USER>/AndroidStudioProjects/OneClickAwayCompose/data"
    name=":data"
    type="LIBRARY"
    maven="OneClickAwayCompose:data:unspecified"
    agpVersion="8.12.0"
    buildFolder="build"
    bootClassPath="/home/<USER>/Android/Sdk/platforms/android-36/android.jar:/home/<USER>/Android/Sdk/build-tools/35.0.0/core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-36"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="debug"/>
</lint-module>
