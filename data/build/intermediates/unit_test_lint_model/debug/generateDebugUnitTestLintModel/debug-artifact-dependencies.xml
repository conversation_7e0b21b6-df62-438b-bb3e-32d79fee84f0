<dependencies>
  <compile
      roots=":@@:data::debug,io.objectbox:objectbox-kotlin:4.0.3@jar,io.objectbox:objectbox-java:4.0.3@jar,io.objectbox:objectbox-android:4.0.3@aar,:@@:domain::debug,com.google.dagger:hilt-android:2.54@aar,androidx.fragment:fragment:1.5.1@aar,androidx.activity:activity:1.5.1@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar,org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.10.1@jar,app.cash.turbine:turbine-jvm:1.2.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1@jar,androidx.core:core-ktx:1.2.0@aar,androidx.loader:loader:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.8.0@aar,androidx.core:core:1.8.0@aar,androidx.annotation:annotation-experimental:1.3.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar,androidx.savedstate:savedstate:1.2.0@aar,io.mockk:mockk-jvm:1.13.14@jar,io.mockk:mockk-dsl-jvm:1.13.14@jar,io.mockk:mockk-agent-jvm:1.13.14@jar,io.mockk:mockk-agent-api-jvm:1.13.14@jar,io.mockk:mockk-core-jvm:1.13.14@jar,org.jetbrains.kotlin:kotlin-stdlib:2.2.0@jar,io.objectbox:objectbox-linux:4.0.3@jar,junit:junit:4.13.2@jar,org.hamcrest:hamcrest-core:1.3@jar,org.jetbrains:annotations:23.0.0@jar,org.objenesis:objenesis:3.3@jar,net.bytebuddy:byte-buddy:1.14.17@jar,net.bytebuddy:byte-buddy-agent:1.14.17@jar,io.objectbox:objectbox-java-api:4.0.3@jar,com.google.dagger:hilt-core:2.54@jar,com.google.code.findbugs:jsr305:3.0.2@jar,com.google.dagger:dagger:2.54@jar,jakarta.inject:jakarta.inject-api:2.0.1@jar,javax.inject:javax.inject:1@jar,org.jspecify:jspecify:1.0.0@jar,com.google.dagger:dagger-lint-aar:2.54@aar,androidx.lifecycle:lifecycle-runtime:2.5.1@aar,androidx.lifecycle:lifecycle-livedata:2.0.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar,androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar,androidx.lifecycle:lifecycle-common:2.5.1@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.arch.core:core-runtime:2.1.0@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.annotation:annotation:1.3.0@jar">
    <dependency
        name=":@@:data::debug"
        simpleName="OneClickAwayCompose:data"/>
    <dependency
        name="io.objectbox:objectbox-kotlin:4.0.3@jar"
        simpleName="io.objectbox:objectbox-kotlin"/>
    <dependency
        name="io.objectbox:objectbox-java:4.0.3@jar"
        simpleName="io.objectbox:objectbox-java"/>
    <dependency
        name="io.objectbox:objectbox-android:4.0.3@aar"
        simpleName="io.objectbox:objectbox-android"/>
    <dependency
        name=":@@:domain::debug"
        simpleName="OneClickAwayCompose:domain"/>
    <dependency
        name="com.google.dagger:hilt-android:2.54@aar"
        simpleName="com.google.dagger:hilt-android"/>
    <dependency
        name="androidx.fragment:fragment:1.5.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.5.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.10.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm"/>
    <dependency
        name="app.cash.turbine:turbine-jvm:1.2.0@jar"
        simpleName="app.cash.turbine:turbine-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.core:core-ktx:1.2.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.8.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.0@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="io.mockk:mockk-jvm:1.13.14@jar"
        simpleName="io.mockk:mockk-jvm"/>
    <dependency
        name="io.mockk:mockk-dsl-jvm:1.13.14@jar"
        simpleName="io.mockk:mockk-dsl-jvm"/>
    <dependency
        name="io.mockk:mockk-agent-jvm:1.13.14@jar"
        simpleName="io.mockk:mockk-agent-jvm"/>
    <dependency
        name="io.mockk:mockk-agent-api-jvm:1.13.14@jar"
        simpleName="io.mockk:mockk-agent-api-jvm"/>
    <dependency
        name="io.mockk:mockk-core-jvm:1.13.14@jar"
        simpleName="io.mockk:mockk-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.2.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="io.objectbox:objectbox-linux:4.0.3@jar"
        simpleName="io.objectbox:objectbox-linux"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="org.objenesis:objenesis:3.3@jar"
        simpleName="org.objenesis:objenesis"/>
    <dependency
        name="net.bytebuddy:byte-buddy:1.14.17@jar"
        simpleName="net.bytebuddy:byte-buddy"/>
    <dependency
        name="net.bytebuddy:byte-buddy-agent:1.14.17@jar"
        simpleName="net.bytebuddy:byte-buddy-agent"/>
    <dependency
        name="io.objectbox:objectbox-java-api:4.0.3@jar"
        simpleName="io.objectbox:objectbox-java-api"/>
    <dependency
        name="com.google.dagger:hilt-core:2.54@jar"
        simpleName="com.google.dagger:hilt-core"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.dagger:dagger:2.54@jar"
        simpleName="com.google.dagger:dagger"/>
    <dependency
        name="jakarta.inject:jakarta.inject-api:2.0.1@jar"
        simpleName="jakarta.inject:jakarta.inject-api"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="com.google.dagger:dagger-lint-aar:2.54@aar"
        simpleName="com.google.dagger:dagger-lint-aar"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.5.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.1.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation:1.3.0@jar"
        simpleName="androidx.annotation:annotation"/>
  </compile>
  <package
      roots=":@@:data::debug,io.objectbox:objectbox-linux:4.0.3@jar,io.mockk:mockk-jvm:1.13.14@jar,junit:junit:4.13.2@jar,app.cash.turbine:turbine-jvm:1.2.0@jar,:@@:domain::debug,com.google.dagger:hilt-android:2.54@aar,androidx.fragment:fragment:1.5.1@aar,androidx.activity:activity:1.5.1@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.10.1@jar,io.mockk:mockk-dsl-jvm:1.13.14@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1@jar,io.objectbox:objectbox-kotlin:4.0.3@jar,io.objectbox:objectbox-android:4.0.3@aar,io.objectbox:objectbox-java:4.0.3@jar,androidx.core:core-ktx:1.2.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.8.0@aar,androidx.core:core:1.8.0@aar,androidx.annotation:annotation-experimental:1.3.1@aar,androidx.savedstate:savedstate:1.2.0@aar,io.mockk:mockk-agent-jvm:1.13.14@jar,io.mockk:mockk-core-jvm:1.13.14@jar,org.jetbrains.kotlin:kotlin-reflect:2.0.0@jar,io.mockk:mockk-agent-api-jvm:1.13.14@jar,org.jetbrains.kotlin:kotlin-stdlib:2.2.0@jar,org.hamcrest:hamcrest-core:1.3@jar,org.greenrobot:essentials:3.1.0@jar,io.objectbox:objectbox-java-api:4.0.3@jar,com.google.dagger:hilt-core:2.54@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.jetbrains:annotations:23.0.0@jar,com.google.dagger:dagger:2.54@jar,com.google.dagger:dagger-lint-aar:2.54@aar,androidx.lifecycle:lifecycle-runtime:2.5.1@aar,androidx.lifecycle:lifecycle-livedata:2.0.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar,androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar,androidx.lifecycle:lifecycle-common:2.5.1@jar,androidx.tracing:tracing:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.arch.core:core-runtime:2.1.0@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.concurrent:concurrent-futures:1.0.0@jar,androidx.annotation:annotation:1.3.0@jar,javax.inject:javax.inject:1@jar,org.junit.jupiter:junit-jupiter-params:5.8.2@jar,org.junit.jupiter:junit-jupiter-engine:5.8.2@jar,org.junit.jupiter:junit-jupiter-api:5.8.2@jar,org.junit.platform:junit-platform-engine:1.8.2@jar,org.junit.platform:junit-platform-commons:1.8.2@jar,org.junit.jupiter:junit-jupiter:5.8.2@jar,jakarta.inject:jakarta.inject-api:2.0.1@jar,org.jspecify:jspecify:1.0.0@jar,org.objenesis:objenesis:3.3@jar,net.bytebuddy:byte-buddy:1.14.17@jar,net.bytebuddy:byte-buddy-agent:1.14.17@jar,org.opentest4j:opentest4j:1.2.0@jar,com.google.guava:listenablefuture:1.0@jar">
    <dependency
        name=":@@:data::debug"
        simpleName="OneClickAwayCompose:data"/>
    <dependency
        name="io.objectbox:objectbox-linux:4.0.3@jar"
        simpleName="io.objectbox:objectbox-linux"/>
    <dependency
        name="io.mockk:mockk-jvm:1.13.14@jar"
        simpleName="io.mockk:mockk-jvm"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="app.cash.turbine:turbine-jvm:1.2.0@jar"
        simpleName="app.cash.turbine:turbine-jvm"/>
    <dependency
        name=":@@:domain::debug"
        simpleName="OneClickAwayCompose:domain"/>
    <dependency
        name="com.google.dagger:hilt-android:2.54@aar"
        simpleName="com.google.dagger:hilt-android"/>
    <dependency
        name="androidx.fragment:fragment:1.5.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.5.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.10.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm"/>
    <dependency
        name="io.mockk:mockk-dsl-jvm:1.13.14@jar"
        simpleName="io.mockk:mockk-dsl-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="io.objectbox:objectbox-kotlin:4.0.3@jar"
        simpleName="io.objectbox:objectbox-kotlin"/>
    <dependency
        name="io.objectbox:objectbox-android:4.0.3@aar"
        simpleName="io.objectbox:objectbox-android"/>
    <dependency
        name="io.objectbox:objectbox-java:4.0.3@jar"
        simpleName="io.objectbox:objectbox-java"/>
    <dependency
        name="androidx.core:core-ktx:1.2.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.8.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.0@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="io.mockk:mockk-agent-jvm:1.13.14@jar"
        simpleName="io.mockk:mockk-agent-jvm"/>
    <dependency
        name="io.mockk:mockk-core-jvm:1.13.14@jar"
        simpleName="io.mockk:mockk-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-reflect:2.0.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-reflect"/>
    <dependency
        name="io.mockk:mockk-agent-api-jvm:1.13.14@jar"
        simpleName="io.mockk:mockk-agent-api-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.2.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="org.greenrobot:essentials:3.1.0@jar"
        simpleName="org.greenrobot:essentials"/>
    <dependency
        name="io.objectbox:objectbox-java-api:4.0.3@jar"
        simpleName="io.objectbox:objectbox-java-api"/>
    <dependency
        name="com.google.dagger:hilt-core:2.54@jar"
        simpleName="com.google.dagger:hilt-core"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.dagger:dagger:2.54@jar"
        simpleName="com.google.dagger:dagger"/>
    <dependency
        name="com.google.dagger:dagger-lint-aar:2.54@aar"
        simpleName="com.google.dagger:dagger-lint-aar"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.5.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.1.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.0.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.annotation:annotation:1.3.0@jar"
        simpleName="androidx.annotation:annotation"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="org.junit.jupiter:junit-jupiter-params:5.8.2@jar"
        simpleName="org.junit.jupiter:junit-jupiter-params"/>
    <dependency
        name="org.junit.jupiter:junit-jupiter-engine:5.8.2@jar"
        simpleName="org.junit.jupiter:junit-jupiter-engine"/>
    <dependency
        name="org.junit.jupiter:junit-jupiter-api:5.8.2@jar"
        simpleName="org.junit.jupiter:junit-jupiter-api"/>
    <dependency
        name="org.junit.platform:junit-platform-engine:1.8.2@jar"
        simpleName="org.junit.platform:junit-platform-engine"/>
    <dependency
        name="org.junit.platform:junit-platform-commons:1.8.2@jar"
        simpleName="org.junit.platform:junit-platform-commons"/>
    <dependency
        name="org.junit.jupiter:junit-jupiter:5.8.2@jar"
        simpleName="org.junit.jupiter:junit-jupiter"/>
    <dependency
        name="jakarta.inject:jakarta.inject-api:2.0.1@jar"
        simpleName="jakarta.inject:jakarta.inject-api"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="org.objenesis:objenesis:3.3@jar"
        simpleName="org.objenesis:objenesis"/>
    <dependency
        name="net.bytebuddy:byte-buddy:1.14.17@jar"
        simpleName="net.bytebuddy:byte-buddy"/>
    <dependency
        name="net.bytebuddy:byte-buddy-agent:1.14.17@jar"
        simpleName="net.bytebuddy:byte-buddy-agent"/>
    <dependency
        name="org.opentest4j:opentest4j:1.2.0@jar"
        simpleName="org.opentest4j:opentest4j"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
  </package>
</dependencies>
