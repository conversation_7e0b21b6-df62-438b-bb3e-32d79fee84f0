package com.htueko.oneclickawaycompose.data.mapper;

@kotlin.Metadata(mv = {2, 2, 0}, k = 2, xi = 48, d1 = {"\u0000\u0016\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\u001a\n\u0010\u0000\u001a\u00020\u0001*\u00020\u0002\u001a\n\u0010\u0003\u001a\u00020\u0002*\u00020\u0001\u001a\u0016\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005*\b\u0012\u0004\u0012\u00020\u00020\u0005\u001a\u0016\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00020\u0005*\b\u0012\u0004\u0012\u00020\u00010\u0005\u00a8\u0006\u0007"}, d2 = {"toEntity", "Lcom/htueko/oneclickawaycompose/data/local/entity/NoteEntity;", "Lcom/htueko/oneclickawaycompose/domain/model/Note;", "toDomain", "toEntityList", "", "toDomainList", "data_debug"})
public final class NoteMapperKt {
    
    /**
     * Converts a domain Note to a data NoteEntity.
     * @return NoteEntity representation of the domain model
     */
    @org.jetbrains.annotations.NotNull()
    public static final com.htueko.oneclickawaycompose.data.local.entity.NoteEntity toEntity(@org.jetbrains.annotations.NotNull()
    com.htueko.oneclickawaycompose.domain.model.Note $this$toEntity) {
        return null;
    }
    
    /**
     * Converts a data NoteEntity to a domain Note.
     * @return Note domain model representation
     */
    @org.jetbrains.annotations.NotNull()
    public static final com.htueko.oneclickawaycompose.domain.model.Note toDomain(@org.jetbrains.annotations.NotNull()
    com.htueko.oneclickawaycompose.data.local.entity.NoteEntity $this$toDomain) {
        return null;
    }
    
    /**
     * Converts a list of domain Notes to a list of NoteEntities.
     * @return List of NoteEntity representations
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.htueko.oneclickawaycompose.data.local.entity.NoteEntity> toEntityList(@org.jetbrains.annotations.NotNull()
    java.util.List<com.htueko.oneclickawaycompose.domain.model.Note> $this$toEntityList) {
        return null;
    }
    
    /**
     * Converts a list of NoteEntities to a list of domain Notes.
     * @return List of Note domain models
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.htueko.oneclickawaycompose.domain.model.Note> toDomainList(@org.jetbrains.annotations.NotNull()
    java.util.List<com.htueko.oneclickawaycompose.data.local.entity.NoteEntity> $this$toDomainList) {
        return null;
    }
}