package com.htueko.oneclickawaycompose.data.di;

/**
 * Hilt module for providing data layer dependencies.
 *
 * WHY: This module centralizes dependency injection configuration for the
 * data layer, ensuring proper scoping and lifecycle management.
 *
 * WHY: @InstallIn(SingletonComponent::class) ensures these dependencies
 * are available throughout the application lifecycle.
 *
 * WHY: @Binds is more efficient than @Provides for simple interface
 * implementations as it generates less code.
 *
 * References:
 * - Hilt Modules: https://dagger.dev/hilt/modules
 * - Dependency Injection: https://developer.android.com/training/dependency-injection/hilt-android
 */
@dagger.Module()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\'\u0018\u0000 \b2\u00020\u0001:\u0001\bB\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0010\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\'\u00a8\u0006\t"}, d2 = {"Lcom/htueko/oneclickawaycompose/data/di/DataModule;", "", "<init>", "()V", "bindNoteRepository", "Lcom/htueko/oneclickawaycompose/domain/repository/NoteRepository;", "noteRepositoryImpl", "Lcom/htueko/oneclickawaycompose/data/repository/NoteRepositoryImpl;", "Companion", "data_release"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public abstract class DataModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.htueko.oneclickawaycompose.data.di.DataModule.Companion Companion = null;
    
    public DataModule() {
        super();
    }
    
    /**
     * Binds the concrete repository implementation to the abstract interface.
     * WHY: This allows the domain layer to depend on abstractions rather than
     * concrete implementations, following the Dependency Inversion Principle.
     */
    @dagger.Binds()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public abstract com.htueko.oneclickawaycompose.domain.repository.NoteRepository bindNoteRepository(@org.jetbrains.annotations.NotNull()
    com.htueko.oneclickawaycompose.data.repository.NoteRepositoryImpl noteRepositoryImpl);
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0012\u0010\u0004\u001a\u00020\u00052\b\b\u0001\u0010\u0006\u001a\u00020\u0007H\u0007\u00a8\u0006\b"}, d2 = {"Lcom/htueko/oneclickawaycompose/data/di/DataModule$Companion;", "", "<init>", "()V", "provideObjectBoxManager", "Lcom/htueko/oneclickawaycompose/data/local/database/ObjectBoxManager;", "context", "Landroid/content/Context;", "data_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * Provides ObjectBoxManager instance.
         * WHY: ObjectBoxManager requires Context which needs to be provided
         * through Hilt's dependency injection system.
         */
        @dagger.Provides()
        @javax.inject.Singleton()
        @org.jetbrains.annotations.NotNull()
        public final com.htueko.oneclickawaycompose.data.local.database.ObjectBoxManager provideObjectBoxManager(@dagger.hilt.android.qualifiers.ApplicationContext()
        @org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
}