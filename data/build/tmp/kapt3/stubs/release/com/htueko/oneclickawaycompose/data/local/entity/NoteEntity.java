package com.htueko.oneclickawaycompose.data.local.entity;

/**
 * ObjectBox entity representing a Note in the local database.
 *
 * WHY: This entity is specific to the data layer and contains ObjectBox
 * annotations. It's separate from the domain model to maintain clean
 * architecture separation and allow for different data representations.
 *
 * WHY: Using @Entity annotation enables ObjectBox code generation for
 * high-performance database operations.
 *
 * WHY: @Id annotation with Long type follows ObjectBox best practices
 * for primary key definition and auto-generation.
 *
 * References:
 * - ObjectBox Entity: https://docs.objectbox.io/android/entity-annotations
 * - Clean Architecture: https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B%\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0007\u0010\bJ\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\'\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u0005H\u00d6\u0001R\u001e\u0010\u0002\u001a\u00020\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\t\u0010\n\"\u0004\b\u000b\u0010\fR\u001a\u0010\u0004\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\r\u0010\u000e\"\u0004\b\u000f\u0010\u0010R\u001a\u0010\u0006\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0011\u0010\n\"\u0004\b\u0012\u0010\f\u00a8\u0006\u001d"}, d2 = {"Lcom/htueko/oneclickawaycompose/data/local/entity/NoteEntity;", "", "id", "", "text", "", "createdAt", "<init>", "(JLjava/lang/String;J)V", "getId", "()J", "setId", "(J)V", "getText", "()Ljava/lang/String;", "setText", "(Ljava/lang/String;)V", "getCreatedAt", "setCreatedAt", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "data_release"})
@io.objectbox.annotation.Entity()
public final class NoteEntity {
    @io.objectbox.annotation.Id()
    private long id;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String text;
    private long createdAt;
    
    public NoteEntity(long id, @org.jetbrains.annotations.NotNull()
    java.lang.String text, long createdAt) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    public final void setId(long p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getText() {
        return null;
    }
    
    public final void setText(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final long getCreatedAt() {
        return 0L;
    }
    
    public final void setCreatedAt(long p0) {
    }
    
    public NoteEntity() {
        super();
    }
    
    public final long component1() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final long component3() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.htueko.oneclickawaycompose.data.local.entity.NoteEntity copy(long id, @org.jetbrains.annotations.NotNull()
    java.lang.String text, long createdAt) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}