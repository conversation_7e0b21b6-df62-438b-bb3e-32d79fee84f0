{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:6905062157516471904", "lastPropertyId": "3:8630346983247306173", "name": "NoteEntity", "properties": [{"id": "1:4500914542639029033", "name": "id", "type": 6, "flags": 1}, {"id": "2:3549254226700104438", "name": "text", "type": 9}, {"id": "3:8630346983247306173", "name": "createdAt", "type": 6}], "relations": []}], "lastEntityId": "1:6905062157516471904", "lastIndexId": "0:0", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [], "retiredRelationUids": [], "version": 1}