package com.htueko.oneclickawaycompose.data.repository

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.htueko.oneclickawaycompose.data.local.database.ObjectBoxManager
import com.htueko.oneclickawaycompose.domain.model.Note
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * Integration tests for NoteRepositoryImpl with real ObjectBox database.
 * 
 * WHY: Integration tests verify that the repository works correctly with
 * the actual ObjectBox database, catching issues that unit tests with
 * mocks might miss.
 * 
 * WHY: Using a real database ensures that ObjectBox entity annotations,
 * queries, and data persistence work correctly in the actual runtime environment.
 * 
 * WHY: These tests validate the complete data flow from repository
 * through ObjectBox to storage and back.
 * 
 * References:
 * - Integration Testing: https://developer.android.com/training/testing/integration-testing
 * - ObjectBox Testing: https://docs.objectbox.io/android/testing
 * - Android Test: https://developer.android.com/training/testing/instrumented-tests
 */
@RunWith(AndroidJUnit4::class)
class NoteRepositoryIntegrationTest {
    
    private lateinit var context: Context
    private lateinit var objectBoxManager: ObjectBoxManager
    private lateinit var repository: NoteRepositoryImpl
    
    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        objectBoxManager = ObjectBoxManager(context)
        repository = NoteRepositoryImpl(objectBoxManager)
        
        // WHY: Clear any existing data to ensure test isolation
        objectBoxManager.getNoteBox().removeAll()
    }
    
    @After
    fun tearDown() {
        // WHY: Clean up database and close resources to prevent memory leaks
        objectBoxManager.getNoteBox().removeAll()
        objectBoxManager.close()
    }
    
    @Test
    fun addNote_shouldPersistNoteAndReturnSuccess() = runTest {
        // Given
        val note = Note(text = "Integration test note", createdAt = System.currentTimeMillis())
        
        // When
        val result = repository.addNote(note)
        
        // Then
        assertTrue(result.isSuccess)
        
        // Verify note was persisted
        val allNotes = repository.getAllNotes().first()
        assertEquals(1, allNotes.size)
        assertEquals(note.text, allNotes.first().text)
        assertEquals(note.createdAt, allNotes.first().createdAt)
    }
    
    @Test
    fun getAllNotes_shouldReturnEmptyList_whenNoNotesExist() = runTest {
        // When
        val notes = repository.getAllNotes().first()
        
        // Then
        assertTrue(notes.isEmpty())
    }
    
    @Test
    fun getAllNotes_shouldReturnAllPersistedNotes() = runTest {
        // Given
        val note1 = Note(text = "First note", createdAt = 1000L)
        val note2 = Note(text = "Second note", createdAt = 2000L)
        
        repository.addNote(note1)
        repository.addNote(note2)
        
        // When
        val notes = repository.getAllNotes().first()
        
        // Then
        assertEquals(2, notes.size)
        assertTrue(notes.any { it.text == "First note" })
        assertTrue(notes.any { it.text == "Second note" })
    }
    
    @Test
    fun deleteNote_shouldRemoveNoteAndReturnSuccess() = runTest {
        // Given
        val note = Note(text = "Note to delete", createdAt = System.currentTimeMillis())
        repository.addNote(note)
        
        val persistedNotes = repository.getAllNotes().first()
        val noteId = persistedNotes.first().id
        
        // When
        val result = repository.deleteNote(noteId)
        
        // Then
        assertTrue(result.isSuccess)
        
        // Verify note was deleted
        val remainingNotes = repository.getAllNotes().first()
        assertTrue(remainingNotes.isEmpty())
    }
    
    @Test
    fun deleteNote_shouldReturnFailure_whenNoteDoesNotExist() = runTest {
        // Given
        val nonExistentNoteId = 999L
        
        // When
        val result = repository.deleteNote(nonExistentNoteId)
        
        // Then
        assertTrue(result.isFailure)
        assertTrue(result.exceptionOrNull() is NoSuchElementException)
    }
    
    @Test
    fun updateNote_shouldModifyExistingNote() = runTest {
        // Given
        val originalNote = Note(text = "Original text", createdAt = 1000L)
        repository.addNote(originalNote)
        
        val persistedNotes = repository.getAllNotes().first()
        val noteId = persistedNotes.first().id
        
        val updatedNote = Note(id = noteId, text = "Updated text", createdAt = 1000L)
        
        // When
        val result = repository.updateNote(updatedNote)
        
        // Then
        assertTrue(result.isSuccess)
        
        // Verify note was updated
        val notes = repository.getAllNotes().first()
        assertEquals(1, notes.size)
        assertEquals("Updated text", notes.first().text)
        assertEquals(noteId, notes.first().id)
    }
    
    @Test
    fun repository_shouldHandleMultipleOperationsCorrectly() = runTest {
        // Given
        val note1 = Note(text = "Note 1", createdAt = 1000L)
        val note2 = Note(text = "Note 2", createdAt = 2000L)
        val note3 = Note(text = "Note 3", createdAt = 3000L)
        
        // When - Add multiple notes
        repository.addNote(note1)
        repository.addNote(note2)
        repository.addNote(note3)
        
        var notes = repository.getAllNotes().first()
        assertEquals(3, notes.size)
        
        // When - Delete one note
        val noteToDelete = notes.first()
        repository.deleteNote(noteToDelete.id)
        
        notes = repository.getAllNotes().first()
        assertEquals(2, notes.size)
        
        // When - Update remaining note
        val noteToUpdate = notes.first()
        val updatedNote = noteToUpdate.copy(text = "Updated note")
        repository.updateNote(updatedNote)
        
        // Then
        notes = repository.getAllNotes().first()
        assertEquals(2, notes.size)
        assertTrue(notes.any { it.text == "Updated note" })
    }
}
