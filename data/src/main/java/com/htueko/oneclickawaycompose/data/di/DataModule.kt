package com.htueko.oneclickawaycompose.data.di

import android.content.Context
import com.htueko.oneclickawaycompose.data.local.database.ObjectBoxManager
import com.htueko.oneclickawaycompose.data.repository.NoteRepositoryImpl
import com.htueko.oneclickawaycompose.domain.repository.NoteRepository
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing data layer dependencies.
 * 
 * WHY: This module centralizes dependency injection configuration for the
 * data layer, ensuring proper scoping and lifecycle management.
 * 
 * WHY: @InstallIn(SingletonComponent::class) ensures these dependencies
 * are available throughout the application lifecycle.
 * 
 * WHY: @Binds is more efficient than @Provides for simple interface
 * implementations as it generates less code.
 * 
 * References:
 * - Hilt Modules: https://dagger.dev/hilt/modules
 * - Dependency Injection: https://developer.android.com/training/dependency-injection/hilt-android
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class DataModule {
    
    /**
     * Binds the concrete repository implementation to the abstract interface.
     * WHY: This allows the domain layer to depend on abstractions rather than
     * concrete implementations, following the Dependency Inversion Principle.
     */
    @Binds
    @Singleton
    abstract fun bindNoteRepository(
        noteRepositoryImpl: NoteRepositoryImpl
    ): NoteRepository
    
    companion object {
        /**
         * Provides ObjectBoxManager instance.
         * WHY: ObjectBoxManager requires Context which needs to be provided
         * through Hilt's dependency injection system.
         */
        @Provides
        @Singleton
        fun provideObjectBoxManager(
            @ApplicationContext context: Context
        ): ObjectBoxManager {
            return ObjectBoxManager(context)
        }
    }
}
