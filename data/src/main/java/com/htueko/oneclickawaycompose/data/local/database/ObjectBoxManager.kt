package com.htueko.oneclickawaycompose.data.local.database

import android.content.Context
import io.objectbox.BoxStore
import io.objectbox.kotlin.boxFor
import com.htueko.oneclickawaycompose.data.local.entity.MyObjectBox
import com.htueko.oneclickawaycompose.data.local.entity.NoteEntity
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Singleton manager for ObjectBox database operations.
 *
 * WHY: Singleton pattern ensures only one BoxStore instance exists throughout
 * the application lifecycle, which is essential for ObjectBox performance and
 * data consistency.
 *
 * WHY: BoxStore is thread-safe and should be reused across the application
 * to avoid the overhead of creating multiple instances.
 *
 * WHY: Using Hilt @Singleton ensures proper lifecycle management and
 * dependency injection throughout the app.
 *
 * References:
 * - ObjectBox BoxStore: https://docs.objectbox.io/android/boxstore
 * - Singleton Pattern: https://en.wikipedia.org/wiki/Singleton_pattern
 * - Hilt Scopes: https://dagger.dev/hilt/scopes
 */
@Singleton
class ObjectBoxManager @Inject constructor(
    private val context: Context
) {

    /**
     * Lazy initialization of BoxStore to defer database creation until first access.
     * WHY: Lazy initialization improves app startup time by deferring database
     * initialization until it's actually needed.
     *
     * WHY: Direct import of MyObjectBox is now possible since ObjectBox generates
     * the class during compilation and it's available in the classpath.
     */
    val boxStore: BoxStore by lazy {
        MyObjectBox.builder()
            .androidContext(context.applicationContext)
            .build()
    }

    /**
     * Provides a Box for NoteEntity operations.
     * WHY: Box provides type-safe CRUD operations for specific entities
     * and is the primary interface for database operations in ObjectBox.
     */
    fun getNoteBox() = boxStore.boxFor<NoteEntity>()

    /**
     * Closes the BoxStore and releases resources.
     * WHY: Proper resource cleanup prevents memory leaks and ensures
     * data integrity when the application is terminated.
     */
    fun close() {
        if (!boxStore.isClosed) {
            boxStore.close()
        }
    }
}
