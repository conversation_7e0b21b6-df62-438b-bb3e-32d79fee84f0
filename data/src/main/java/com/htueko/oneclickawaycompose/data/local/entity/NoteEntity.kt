package com.htueko.oneclickawaycompose.data.local.entity

import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id

/**
 * ObjectBox entity representing a Note in the local database.
 * 
 * WHY: This entity is specific to the data layer and contains ObjectBox
 * annotations. It's separate from the domain model to maintain clean
 * architecture separation and allow for different data representations.
 * 
 * WHY: Using @Entity annotation enables ObjectBox code generation for
 * high-performance database operations.
 * 
 * WHY: @Id annotation with Long type follows ObjectBox best practices
 * for primary key definition and auto-generation.
 * 
 * References:
 * - ObjectBox Entity: https://docs.objectbox.io/android/entity-annotations
 * - Clean Architecture: https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html
 */
@Entity
data class NoteEntity(
    @Id
    var id: Long = 0,
    
    var text: String = "",
    
    var createdAt: Long = 0
)
