package com.htueko.oneclickawaycompose.data.mapper

import com.htueko.oneclickawaycompose.data.local.entity.NoteEntity
import com.htueko.oneclickawaycompose.domain.model.Note

/**
 * Mapper functions to convert between domain models and data entities.
 * 
 * WHY: Mappers maintain separation between domain and data layers,
 * allowing each layer to have its own optimal data representation
 * without tight coupling.
 * 
 * WHY: Extension functions provide a clean, readable API for conversions
 * and can be easily tested in isolation.
 * 
 * WHY: Bidirectional mapping enables data flow in both directions
 * while maintaining type safety and explicit conversions.
 * 
 * References:
 * - Clean Architecture: https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html
 * - Kotlin Extension Functions: https://kotlinlang.org/docs/extensions.html
 */

/**
 * Converts a domain Note to a data NoteEntity.
 * @return NoteEntity representation of the domain model
 */
fun Note.toEntity(): NoteEntity {
    return NoteEntity(
        id = this.id,
        text = this.text,
        createdAt = this.createdAt
    )
}

/**
 * Converts a data NoteEntity to a domain Note.
 * @return Note domain model representation
 */
fun NoteEntity.toDomain(): Note {
    return Note(
        id = this.id,
        text = this.text,
        createdAt = this.createdAt
    )
}

/**
 * Converts a list of domain Notes to a list of NoteEntities.
 * @return List of NoteEntity representations
 */
fun List<Note>.toEntityList(): List<NoteEntity> {
    return this.map { it.toEntity() }
}

/**
 * Converts a list of NoteEntities to a list of domain Notes.
 * @return List of Note domain models
 */
fun List<NoteEntity>.toDomainList(): List<Note> {
    return this.map { it.toDomain() }
}
