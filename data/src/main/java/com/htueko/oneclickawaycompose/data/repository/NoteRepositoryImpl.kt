package com.htueko.oneclickawaycompose.data.repository

import com.htueko.oneclickawaycompose.data.local.database.ObjectBoxManager
import com.htueko.oneclickawaycompose.data.mapper.toDomain
import com.htueko.oneclickawaycompose.data.mapper.toDomainList
import com.htueko.oneclickawaycompose.data.mapper.toEntity
import com.htueko.oneclickawaycompose.domain.model.Note
import com.htueko.oneclickawaycompose.domain.repository.NoteRepository
import io.objectbox.kotlin.flow
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of NoteRepository using ObjectBox as the data source.
 *
 * WHY: This implementation provides the concrete data access logic while
 * implementing the abstract repository interface from the domain layer,
 * following the Dependency Inversion Principle.
 *
 * WHY: Using Dispatchers.IO ensures database operations are performed on
 * a background thread optimized for I/O operations, preventing ANR issues.
 *
 * WHY: ObjectBox Flow integration provides reactive data updates, automatically
 * notifying observers when data changes in the database.
 *
 * WHY: Result wrapper provides explicit error handling and makes failures
 * visible to the calling code.
 *
 * References:
 * - Repository Pattern: https://developer.android.com/topic/architecture/data-layer
 * - ObjectBox Flow: https://docs.objectbox.io/android/data-flow
 * - Kotlin Coroutines: https://kotlinlang.org/docs/coroutines-overview.html
 * - SOLID Principles: https://en.wikipedia.org/wiki/SOLID
 */
@OptIn(ExperimentalCoroutinesApi::class)
@Singleton
class NoteRepositoryImpl @Inject constructor(
    private val objectBoxManager: ObjectBoxManager
) : NoteRepository {
    
    private val noteBox = objectBoxManager.getNoteBox()
    
    override fun getAllNotes(): Flow<List<Note>> {
        // WHY: ObjectBox flow() provides reactive updates when data changes
        // and automatically switches to background thread for database queries
        return noteBox.query().build().flow().map { entities ->
            entities.toDomainList()
        }
    }
    
    override suspend fun addNote(note: Note): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val entity = note.toEntity()
            noteBox.put(entity)
            Result.success(Unit)
        } catch (e: Exception) {
            // WHY: Catch and wrap exceptions to provide consistent error handling
            // across the application and prevent crashes
            Result.failure(e)
        }
    }
    
    override suspend fun deleteNote(noteId: Long): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val removed = noteBox.remove(noteId)
            if (removed) {
                Result.success(Unit)
            } else {
                Result.failure(NoSuchElementException("Note with ID $noteId not found"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun updateNote(note: Note): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val entity = note.toEntity()
            noteBox.put(entity)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
