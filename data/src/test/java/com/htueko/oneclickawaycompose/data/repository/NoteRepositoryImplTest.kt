package com.htueko.oneclickawaycompose.data.repository

import com.htueko.oneclickawaycompose.data.local.database.ObjectBoxManager
import com.htueko.oneclickawaycompose.data.local.entity.NoteEntity
import com.htueko.oneclickawaycompose.domain.model.Note
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.objectbox.Box
import io.objectbox.kotlin.flow
import io.objectbox.query.Query
import io.objectbox.query.QueryBuilder
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * Unit tests for NoteRepositoryImpl.
 * 
 * WHY: Repository testing ensures data layer operations work correctly
 * and properly handle both success and failure scenarios.
 * 
 * WHY: Mocking ObjectBox components allows us to test repository logic
 * without requiring a real database, making tests faster and more reliable.
 * 
 * WHY: Testing the mapping between domain and data models ensures
 * data integrity across layer boundaries.
 * 
 * References:
 * - Repository Testing: https://developer.android.com/topic/architecture/data-layer#test-data-layer
 * - MockK: https://mockk.io/
 * - ObjectBox Testing: https://docs.objectbox.io/android/testing
 */
class NoteRepositoryImplTest {
    
    private lateinit var objectBoxManager: ObjectBoxManager
    private lateinit var noteBox: Box<NoteEntity>
    private lateinit var repository: NoteRepositoryImpl
    
    @Before
    fun setup() {
        objectBoxManager = mockk()
        noteBox = mockk(relaxed = true)
        every { objectBoxManager.getNoteBox() } returns noteBox
        
        repository = NoteRepositoryImpl(objectBoxManager)
    }
    
    @Test
    fun `getAllNotes should return mapped domain models from ObjectBox flow`() = runTest {
        // Given
        val entities = listOf(
            NoteEntity(id = 1, text = "Note 1", createdAt = 1000L),
            NoteEntity(id = 2, text = "Note 2", createdAt = 2000L)
        )
        val expectedNotes = listOf(
            Note(id = 1, text = "Note 1", createdAt = 1000L),
            Note(id = 2, text = "Note 2", createdAt = 2000L)
        )
        
        val queryBuilder = mockk<QueryBuilder<NoteEntity>>()
        val query = mockk<Query<NoteEntity>>()
        
        every { noteBox.query() } returns queryBuilder
        every { queryBuilder.build() } returns query
        every { query.flow() } returns flowOf(entities)
        
        // When
        val result = repository.getAllNotes().toList()
        
        // Then
        assertEquals(1, result.size)
        assertEquals(expectedNotes, result.first())
    }
    
    @Test
    fun `addNote should put entity to ObjectBox and return success`() = runTest {
        // Given
        val note = Note(id = 0, text = "Test note", createdAt = 1000L)
        every { noteBox.put(any<NoteEntity>()) } returns 1L
        
        // When
        val result = repository.addNote(note)
        
        // Then
        assertTrue(result.isSuccess)
        verify { noteBox.put(match<NoteEntity> { it.text == note.text && it.createdAt == note.createdAt }) }
    }
    
    @Test
    fun `addNote should return failure when ObjectBox throws exception`() = runTest {
        // Given
        val note = Note(id = 0, text = "Test note", createdAt = 1000L)
        val exception = RuntimeException("Database error")
        every { noteBox.put(any<NoteEntity>()) } throws exception
        
        // When
        val result = repository.addNote(note)
        
        // Then
        assertTrue(result.isFailure)
        assertEquals(exception, result.exceptionOrNull())
    }
    
    @Test
    fun `deleteNote should remove entity from ObjectBox and return success`() = runTest {
        // Given
        val noteId = 1L
        every { noteBox.remove(noteId) } returns true
        
        // When
        val result = repository.deleteNote(noteId)
        
        // Then
        assertTrue(result.isSuccess)
        verify { noteBox.remove(noteId) }
    }
    
    @Test
    fun `deleteNote should return failure when note not found`() = runTest {
        // Given
        val noteId = 1L
        every { noteBox.remove(noteId) } returns false
        
        // When
        val result = repository.deleteNote(noteId)
        
        // Then
        assertTrue(result.isFailure)
        assertTrue(result.exceptionOrNull() is NoSuchElementException)
    }
    
    @Test
    fun `deleteNote should return failure when ObjectBox throws exception`() = runTest {
        // Given
        val noteId = 1L
        val exception = RuntimeException("Database error")
        every { noteBox.remove(noteId) } throws exception
        
        // When
        val result = repository.deleteNote(noteId)
        
        // Then
        assertTrue(result.isFailure)
        assertEquals(exception, result.exceptionOrNull())
    }
    
    @Test
    fun `updateNote should put entity to ObjectBox and return success`() = runTest {
        // Given
        val note = Note(id = 1, text = "Updated note", createdAt = 1000L)
        every { noteBox.put(any<NoteEntity>()) } returns 1L
        
        // When
        val result = repository.updateNote(note)
        
        // Then
        assertTrue(result.isSuccess)
        verify { noteBox.put(match<NoteEntity> { it.id == note.id && it.text == note.text }) }
    }
}
