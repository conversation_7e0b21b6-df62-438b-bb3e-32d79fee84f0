<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-livedata-core:2.5.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/bf78256ef2c7efca284702f1254a90e0/transformed/lifecycle-livedata-core-2.5.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-livedata-core:2.5.1" from-dependency="true" generated-set="androidx.lifecycle:lifecycle-livedata-core:2.5.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/bf78256ef2c7efca284702f1254a90e0/transformed/lifecycle-livedata-core-2.5.1/res"><file path="/home/<USER>/.gradle/caches/8.13/transforms/bf78256ef2c7efca284702f1254a90e0/transformed/lifecycle-livedata-core-2.5.1/res/values/values.xml" qualifiers=""/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-runtime:2.5.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/1da5024a30b4b02bc362cb9234b7be8b/transformed/lifecycle-runtime-2.5.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-runtime:2.5.1" from-dependency="true" generated-set="androidx.lifecycle:lifecycle-runtime:2.5.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/1da5024a30b4b02bc362cb9234b7be8b/transformed/lifecycle-runtime-2.5.1/res"><file path="/home/<USER>/.gradle/caches/8.13/transforms/1da5024a30b4b02bc362cb9234b7be8b/transformed/lifecycle-runtime-2.5.1/res/values/values.xml" qualifiers=""><id name="view_tree_lifecycle_owner"/></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.savedstate:savedstate:1.2.0$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/d1f701969a94f6b2a3933c151b58d553/transformed/savedstate-1.2.0/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.savedstate:savedstate:1.2.0" from-dependency="true" generated-set="androidx.savedstate:savedstate:1.2.0$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/d1f701969a94f6b2a3933c151b58d553/transformed/savedstate-1.2.0/res"><file path="/home/<USER>/.gradle/caches/8.13/transforms/d1f701969a94f6b2a3933c151b58d553/transformed/savedstate-1.2.0/res/values/values.xml" qualifiers=""><id name="view_tree_saved_state_registry_owner"/></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.annotation:annotation-experimental:1.3.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/e93bc51223aaf181fa7cf4c30c301d49/transformed/annotation-experimental-1.3.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.annotation:annotation-experimental:1.3.1" from-dependency="true" generated-set="androidx.annotation:annotation-experimental:1.3.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/e93bc51223aaf181fa7cf4c30c301d49/transformed/annotation-experimental-1.3.1/res"><file path="/home/<USER>/.gradle/caches/8.13/transforms/e93bc51223aaf181fa7cf4c30c301d49/transformed/annotation-experimental-1.3.1/res/values/values.xml" qualifiers=""/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.core:core:1.8.0$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.core:core:1.8.0" from-dependency="true" generated-set="androidx.core:core:1.8.0$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res"><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-es/values-es.xml" qualifiers="es"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-ky/values-ky.xml" qualifiers="ky"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-en-rGB/values-en-rGB.xml" qualifiers="en-rGB"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-af/values-af.xml" qualifiers="af"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-ml/values-ml.xml" qualifiers="ml"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file name="notification_bg_normal_pressed" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable-xhdpi-v4/notification_bg_normal_pressed.9.png" qualifiers="xhdpi-v4" type="drawable"/><file name="notification_bg_low_normal" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable-xhdpi-v4/notification_bg_low_normal.9.png" qualifiers="xhdpi-v4" type="drawable"/><file name="notification_bg_normal" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable-xhdpi-v4/notification_bg_normal.9.png" qualifiers="xhdpi-v4" type="drawable"/><file name="notification_bg_low_pressed" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable-xhdpi-v4/notification_bg_low_pressed.9.png" qualifiers="xhdpi-v4" type="drawable"/><file name="notify_panel_notification_icon_bg" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable-xhdpi-v4/notify_panel_notification_icon_bg.png" qualifiers="xhdpi-v4" type="drawable"/><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-my/values-my.xml" qualifiers="my"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"၉၉၉+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-pt-rPT/values-pt-rPT.xml" qualifiers="pt-rPT"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-sw/values-sw.xml" qualifiers="sw"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-ur/values-ur.xml" qualifiers="ur"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"+999"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-et/values-et.xml" qualifiers="et"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-it/values-it.xml" qualifiers="it"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-sr/values-sr.xml" qualifiers="sr"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-el/values-el.xml" qualifiers="el"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-en-rIN/values-en-rIN.xml" qualifiers="en-rIN"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-nb/values-nb.xml" qualifiers="nb"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-vi/values-vi.xml" qualifiers="vi"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-fr-rCA/values-fr-rCA.xml" qualifiers="fr-rCA"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-es-rUS/values-es-rUS.xml" qualifiers="es-rUS"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-as/values-as.xml" qualifiers="as"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"৯৯৯+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-en-rAU/values-en-rAU.xml" qualifiers="en-rAU"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values/values.xml" qualifiers=""><attr format="reference" name="nestedScrollViewStyle"/><color name="androidx_core_ripple_material_light">#1f000000</color><color name="androidx_core_secondary_text_default_material_light">#8a000000</color><color name="notification_action_color_filter">#ffffffff</color><color name="notification_icon_bg_color">#ff9e9e9e</color><dimen name="compat_button_inset_horizontal_material">4dp</dimen><dimen name="compat_button_inset_vertical_material">6dp</dimen><dimen name="compat_button_padding_horizontal_material">8dp</dimen><dimen name="compat_button_padding_vertical_material">4dp</dimen><dimen name="compat_control_corner_material">2dp</dimen><dimen name="compat_notification_large_icon_max_height">320dp</dimen><dimen name="compat_notification_large_icon_max_width">320dp</dimen><dimen name="notification_action_icon_size">32dp</dimen><dimen name="notification_action_text_size">13sp</dimen><dimen name="notification_big_circle_margin">12dp</dimen><dimen name="notification_content_margin_start">8dp</dimen><dimen name="notification_large_icon_height">64dp</dimen><dimen name="notification_large_icon_width">64dp</dimen><dimen name="notification_main_column_padding_top">10dp</dimen><dimen name="notification_media_narrow_margin">@dimen/notification_content_margin_start</dimen><dimen name="notification_right_icon_size">16dp</dimen><dimen name="notification_right_side_padding_top">2dp</dimen><dimen name="notification_small_icon_background_padding">3dp</dimen><dimen name="notification_small_icon_size_as_large">24dp</dimen><dimen name="notification_subtext_size">13sp</dimen><dimen name="notification_top_pad">10dp</dimen><dimen name="notification_top_pad_large_text">5dp</dimen><drawable name="notification_template_icon_bg">#3333B5E5</drawable><drawable name="notification_template_icon_low_bg">#0cffffff</drawable><item name="accessibility_action_clickable_span" type="id"/><item name="accessibility_custom_action_0" type="id"/><item name="accessibility_custom_action_1" type="id"/><item name="accessibility_custom_action_10" type="id"/><item name="accessibility_custom_action_11" type="id"/><item name="accessibility_custom_action_12" type="id"/><item name="accessibility_custom_action_13" type="id"/><item name="accessibility_custom_action_14" type="id"/><item name="accessibility_custom_action_15" type="id"/><item name="accessibility_custom_action_16" type="id"/><item name="accessibility_custom_action_17" type="id"/><item name="accessibility_custom_action_18" type="id"/><item name="accessibility_custom_action_19" type="id"/><item name="accessibility_custom_action_2" type="id"/><item name="accessibility_custom_action_20" type="id"/><item name="accessibility_custom_action_21" type="id"/><item name="accessibility_custom_action_22" type="id"/><item name="accessibility_custom_action_23" type="id"/><item name="accessibility_custom_action_24" type="id"/><item name="accessibility_custom_action_25" type="id"/><item name="accessibility_custom_action_26" type="id"/><item name="accessibility_custom_action_27" type="id"/><item name="accessibility_custom_action_28" type="id"/><item name="accessibility_custom_action_29" type="id"/><item name="accessibility_custom_action_3" type="id"/><item name="accessibility_custom_action_30" type="id"/><item name="accessibility_custom_action_31" type="id"/><item name="accessibility_custom_action_4" type="id"/><item name="accessibility_custom_action_5" type="id"/><item name="accessibility_custom_action_6" type="id"/><item name="accessibility_custom_action_7" type="id"/><item name="accessibility_custom_action_8" type="id"/><item name="accessibility_custom_action_9" type="id"/><item name="line1" type="id"/><item name="line3" type="id"/><item name="tag_accessibility_actions" type="id"/><item name="tag_accessibility_clickable_spans" type="id"/><item name="tag_accessibility_heading" type="id"/><item name="tag_accessibility_pane_title" type="id"/><item name="tag_on_apply_window_listener" type="id"/><item name="tag_on_receive_content_listener" type="id"/><item name="tag_on_receive_content_mime_types" type="id"/><item name="tag_screen_reader_focusable" type="id"/><item name="tag_state_description" type="id"/><item name="tag_transition_group" type="id"/><item name="tag_unhandled_key_event_manager" type="id"/><item name="tag_unhandled_key_listeners" type="id"/><item name="tag_window_insets_animation_callback" type="id"/><item name="text" type="id"/><item name="text2" type="id"/><item name="title" type="id"/><integer name="status_bar_notification_info_maxnum">999</integer><string name="status_bar_notification_info_overflow">999+</string><style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.StatusBar.EventContent"/><style name="TextAppearance.Compat.Notification.Info">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style><style name="TextAppearance.Compat.Notification.Line2" parent="TextAppearance.Compat.Notification.Info"/><style name="TextAppearance.Compat.Notification.Time">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style><style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.StatusBar.EventContent.Title"/><style name="Widget.Compat.NotificationActionContainer" parent=""/><style name="Widget.Compat.NotificationActionText" parent=""/><declare-styleable name="Capability">
        
        <attr format="reference" name="queryPatterns"/>
        
        <attr format="boolean" name="shortcutMatchRequired"/>
    </declare-styleable><declare-styleable name="ColorStateListItem">
        
        <attr name="android:color"/>
        
        <attr format="float" name="alpha"/>
        <attr name="android:alpha"/>
        
        <attr format="float" name="lStar"/>
        <attr name="android:lStar"/>
    </declare-styleable><declare-styleable name="FontFamily">
        
        <attr format="string" name="fontProviderAuthority"/>
        
        <attr format="string" name="fontProviderPackage"/>
        
        <attr format="string" name="fontProviderQuery"/>
        
        <attr format="reference" name="fontProviderCerts"/>
        
        <attr name="fontProviderFetchStrategy">
            
            <enum name="blocking" value="0"/>
            
            <enum name="async" value="1"/>
        </attr>
        
        <attr format="integer" name="fontProviderFetchTimeout">
            
            <enum name="forever" value="-1"/>
        </attr>
        
        <attr format="string" name="fontProviderSystemFontFamily"/>
    </declare-styleable><declare-styleable name="FontFamilyFont">
        
        <attr name="fontStyle">
            <enum name="normal" value="0"/>
            <enum name="italic" value="1"/>
        </attr>
        
        <attr format="reference" name="font"/>
        
        <attr format="integer" name="fontWeight"/>
        
        <attr format="string" name="fontVariationSettings"/>
        
        <attr format="integer" name="ttcIndex"/>
        
        <attr name="android:fontStyle"/>
        <attr name="android:font"/>
        <attr name="android:fontWeight"/>
        <attr name="android:fontVariationSettings"/>
        <attr name="android:ttcIndex"/>
    </declare-styleable><declare-styleable name="GradientColor">
        
        <attr name="android:startColor"/>
        
        <attr name="android:centerColor"/>
        
        <attr name="android:endColor"/>
        
        <attr name="android:type"/>

        
        
        <attr name="android:gradientRadius"/>

        
        
        <attr name="android:centerX"/>
        
        <attr name="android:centerY"/>

        
        
        <attr name="android:startX"/>
        
        <attr name="android:startY"/>
        
        <attr name="android:endX"/>
        
        <attr name="android:endY"/>

        
        <attr name="android:tileMode"/>
    </declare-styleable><declare-styleable name="GradientColorItem">
        
        <attr name="android:offset"/>
        
        <attr name="android:color"/>
    </declare-styleable></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-ja/values-ja.xml" qualifiers="ja"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-tl/values-tl.xml" qualifiers="tl"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-sl/values-sl.xml" qualifiers="sl"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-kn/values-kn.xml" qualifiers="kn"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-az/values-az.xml" qualifiers="az"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-lo/values-lo.xml" qualifiers="lo"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-mk/values-mk.xml" qualifiers="mk"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-ko/values-ko.xml" qualifiers="ko"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-v16/values-v16.xml" qualifiers="v16"><dimen name="notification_right_side_padding_top">4dp</dimen></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-tr/values-tr.xml" qualifiers="tr"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-ms/values-ms.xml" qualifiers="ms"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-am/values-am.xml" qualifiers="am"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file name="notification_bg_normal_pressed" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable-hdpi-v4/notification_bg_normal_pressed.9.png" qualifiers="hdpi-v4" type="drawable"/><file name="notification_bg_low_normal" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable-hdpi-v4/notification_bg_low_normal.9.png" qualifiers="hdpi-v4" type="drawable"/><file name="notification_bg_normal" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable-hdpi-v4/notification_bg_normal.9.png" qualifiers="hdpi-v4" type="drawable"/><file name="notification_bg_low_pressed" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable-hdpi-v4/notification_bg_low_pressed.9.png" qualifiers="hdpi-v4" type="drawable"/><file name="notify_panel_notification_icon_bg" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable-hdpi-v4/notify_panel_notification_icon_bg.png" qualifiers="hdpi-v4" type="drawable"/><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-da/values-da.xml" qualifiers="da"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-zh-rHK/values-zh-rHK.xml" qualifiers="zh-rHK"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-b+sr+Latn/values-b+sr+Latn.xml" qualifiers="b+sr+Latn"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-sv/values-sv.xml" qualifiers="sv"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-si/values-si.xml" qualifiers="si"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file name="notification_action" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/layout-v21/notification_action.xml" qualifiers="v21" type="layout"/><file name="notification_template_icon_group" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/layout-v21/notification_template_icon_group.xml" qualifiers="v21" type="layout"/><file name="notification_template_custom_big" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/layout-v21/notification_template_custom_big.xml" qualifiers="v21" type="layout"/><file name="notification_action_tombstone" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/layout-v21/notification_action_tombstone.xml" qualifiers="v21" type="layout"/><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-pa/values-pa.xml" qualifiers="pa"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-sq/values-sq.xml" qualifiers="sq"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-ta/values-ta.xml" qualifiers="ta"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-fr/values-fr.xml" qualifiers="fr"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-hu/values-hu.xml" qualifiers="hu"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-pl/values-pl.xml" qualifiers="pl"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file name="notification_template_custom_big" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/layout-v16/notification_template_custom_big.xml" qualifiers="v16" type="layout"/><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-gl/values-gl.xml" qualifiers="gl"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">">999"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-pt/values-pt.xml" qualifiers="pt"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-fi/values-fi.xml" qualifiers="fi"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-hr/values-hr.xml" qualifiers="hr"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-is/values-is.xml" qualifiers="is"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-ar/values-ar.xml" qualifiers="ar"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-ne/values-ne.xml" qualifiers="ne"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"९९९+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-sk/values-sk.xml" qualifiers="sk"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-fa/values-fa.xml" qualifiers="fa"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-nl/values-nl.xml" qualifiers="nl"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-ru/values-ru.xml" qualifiers="ru"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">">999"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-in/values-in.xml" qualifiers="in"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-mn/values-mn.xml" qualifiers="mn"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-hy/values-hy.xml" qualifiers="hy"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file name="notification_action_background" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable-v21/notification_action_background.xml" qualifiers="v21" type="drawable"/><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-zu/values-zu.xml" qualifiers="zu"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-or/values-or.xml" qualifiers="or"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-ro/values-ro.xml" qualifiers="ro"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-uz/values-uz.xml" qualifiers="uz"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-pt-rBR/values-pt-rBR.xml" qualifiers="pt-rBR"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-mr/values-mr.xml" qualifiers="mr"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"९९९+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-de/values-de.xml" qualifiers="de"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-bs/values-bs.xml" qualifiers="bs"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-lv/values-lv.xml" qualifiers="lv"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-bn/values-bn.xml" qualifiers="bn"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"৯৯৯+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-ka/values-ka.xml" qualifiers="ka"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-lt/values-lt.xml" qualifiers="lt"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-iw/values-iw.xml" qualifiers="iw"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-be/values-be.xml" qualifiers="be"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-v21/values-v21.xml" qualifiers="v21"><color name="notification_action_color_filter">@color/androidx_core_secondary_text_default_material_light</color><dimen name="notification_content_margin_start">0dp</dimen><dimen name="notification_main_column_padding_top">0dp</dimen><dimen name="notification_media_narrow_margin">12dp</dimen><style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.Material.Notification"/><style name="TextAppearance.Compat.Notification.Info" parent="@android:style/TextAppearance.Material.Notification.Info"/><style name="TextAppearance.Compat.Notification.Time" parent="@android:style/TextAppearance.Material.Notification.Time"/><style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.Material.Notification.Title"/><style name="Widget.Compat.NotificationActionContainer" parent="">
        <item name="android:background">@drawable/notification_action_background</item>
    </style><style name="Widget.Compat.NotificationActionText" parent="">
        <item name="android:textAppearance">?android:attr/textAppearanceButton</item>
        <item name="android:textColor">@color/androidx_core_secondary_text_default_material_light</item>
        <item name="android:textSize">@dimen/notification_action_text_size</item>
    </style></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-gu/values-gu.xml" qualifiers="gu"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-cs/values-cs.xml" qualifiers="cs"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-ca/values-ca.xml" qualifiers="ca"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-kk/values-kk.xml" qualifiers="kk"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-uk/values-uk.xml" qualifiers="uk"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file name="notification_tile_bg" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable/notification_tile_bg.xml" qualifiers="" type="drawable"/><file name="notification_icon_background" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable/notification_icon_background.xml" qualifiers="" type="drawable"/><file name="notification_bg_low" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable/notification_bg_low.xml" qualifiers="" type="drawable"/><file name="notification_bg" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable/notification_bg.xml" qualifiers="" type="drawable"/><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-zh-rCN/values-zh-rCN.xml" qualifiers="zh-rCN"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-bg/values-bg.xml" qualifiers="bg"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-en-rCA/values-en-rCA.xml" qualifiers="en-rCA"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file name="notification_action" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/layout/notification_action.xml" qualifiers="" type="layout"/><file name="custom_dialog" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/layout/custom_dialog.xml" qualifiers="" type="layout"/><file name="notification_template_icon_group" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/layout/notification_template_icon_group.xml" qualifiers="" type="layout"/><file name="notification_template_part_chronometer" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/layout/notification_template_part_chronometer.xml" qualifiers="" type="layout"/><file name="notification_template_custom_big" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/layout/notification_template_custom_big.xml" qualifiers="" type="layout"/><file name="notification_template_part_time" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/layout/notification_template_part_time.xml" qualifiers="" type="layout"/><file name="notification_action_tombstone" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/layout/notification_action_tombstone.xml" qualifiers="" type="layout"/><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-hi/values-hi.xml" qualifiers="hi"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-te/values-te.xml" qualifiers="te"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-th/values-th.xml" qualifiers="th"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file name="notification_bg_normal_pressed" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable-mdpi-v4/notification_bg_normal_pressed.9.png" qualifiers="mdpi-v4" type="drawable"/><file name="notification_bg_low_normal" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable-mdpi-v4/notification_bg_low_normal.9.png" qualifiers="mdpi-v4" type="drawable"/><file name="notification_bg_normal" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable-mdpi-v4/notification_bg_normal.9.png" qualifiers="mdpi-v4" type="drawable"/><file name="notification_bg_low_pressed" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable-mdpi-v4/notification_bg_low_pressed.9.png" qualifiers="mdpi-v4" type="drawable"/><file name="notify_panel_notification_icon_bg" path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/drawable-mdpi-v4/notify_panel_notification_icon_bg.png" qualifiers="mdpi-v4" type="drawable"/><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-eu/values-eu.xml" qualifiers="eu"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-km/values-km.xml" qualifiers="km"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-zh-rTW/values-zh-rTW.xml" qualifiers="zh-rTW"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"999+"</string></file><file path="/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-en-rXC/values-en-rXC.xml" qualifiers="en-rXC"><string msgid="6277540029070332960" name="status_bar_notification_info_overflow">"‎‏‎‎‎‎‎‏‎‏‏‏‎‎‎‎‎‎‏‎‎‏‎‎‎‎‏‏‏‏‏‏‏‏‎‏‎‏‏‏‎‎‎‏‏‏‏‎‎‏‎‎‏‏‎‏‎‏‏‎‎‏‎‏‏‎‎‎‏‎‎‎‎‎‎‎‏‎‎‎‎‏‎‏‎‏‎‎‎‎‏‎‎‎‎‎‎999+‎‏‎‎‏‎"</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/83c5ca48af4eea9434869b6f299f82fe/transformed/lifecycle-viewmodel-savedstate-2.5.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1" from-dependency="true" generated-set="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/83c5ca48af4eea9434869b6f299f82fe/transformed/lifecycle-viewmodel-savedstate-2.5.1/res"><file path="/home/<USER>/.gradle/caches/8.13/transforms/83c5ca48af4eea9434869b6f299f82fe/transformed/lifecycle-viewmodel-savedstate-2.5.1/res/values/values.xml" qualifiers=""/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-viewmodel:2.5.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/862bb3137af1ebbe770e259225cdba37/transformed/lifecycle-viewmodel-2.5.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.lifecycle:lifecycle-viewmodel:2.5.1" from-dependency="true" generated-set="androidx.lifecycle:lifecycle-viewmodel:2.5.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/862bb3137af1ebbe770e259225cdba37/transformed/lifecycle-viewmodel-2.5.1/res"><file path="/home/<USER>/.gradle/caches/8.13/transforms/862bb3137af1ebbe770e259225cdba37/transformed/lifecycle-viewmodel-2.5.1/res/values/values.xml" qualifiers=""><id name="view_tree_view_model_store_owner"/></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.activity:activity:1.5.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/2b021cf59ac32486003571b26a8d45e5/transformed/activity-1.5.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.activity:activity:1.5.1" from-dependency="true" generated-set="androidx.activity:activity:1.5.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/2b021cf59ac32486003571b26a8d45e5/transformed/activity-1.5.1/res"><file path="/home/<USER>/.gradle/caches/8.13/transforms/2b021cf59ac32486003571b26a8d45e5/transformed/activity-1.5.1/res/values/values.xml" qualifiers=""><id name="view_tree_on_back_pressed_dispatcher_owner"/></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.fragment:fragment:1.5.1$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/3f662f34ce46601d59f8125b7629f881/transformed/fragment-1.5.1/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="androidx.fragment:fragment:1.5.1" from-dependency="true" generated-set="androidx.fragment:fragment:1.5.1$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/8.13/transforms/3f662f34ce46601d59f8125b7629f881/transformed/fragment-1.5.1/res"><file path="/home/<USER>/.gradle/caches/8.13/transforms/3f662f34ce46601d59f8125b7629f881/transformed/fragment-1.5.1/res/values/values.xml" qualifiers=""><item name="fragment_container_view_tag" type="id"/><item name="special_effects_controller_view_tag" type="id"/><item name="visible_removing_fragment_view_tag" type="id"/><declare-styleable name="Fragment">
        <attr name="android:name"/>
        <attr name="android:id"/>
        <attr name="android:tag"/>
    </declare-styleable><declare-styleable name="FragmentContainerView">
        <attr name="android:name"/>
        <attr name="android:tag"/>
    </declare-styleable></file><file name="fragment_open_enter" path="/home/<USER>/.gradle/caches/8.13/transforms/3f662f34ce46601d59f8125b7629f881/transformed/fragment-1.5.1/res/animator/fragment_open_enter.xml" qualifiers="" type="animator"/><file name="fragment_fade_enter" path="/home/<USER>/.gradle/caches/8.13/transforms/3f662f34ce46601d59f8125b7629f881/transformed/fragment-1.5.1/res/animator/fragment_fade_enter.xml" qualifiers="" type="animator"/><file name="fragment_fade_exit" path="/home/<USER>/.gradle/caches/8.13/transforms/3f662f34ce46601d59f8125b7629f881/transformed/fragment-1.5.1/res/animator/fragment_fade_exit.xml" qualifiers="" type="animator"/><file name="fragment_close_enter" path="/home/<USER>/.gradle/caches/8.13/transforms/3f662f34ce46601d59f8125b7629f881/transformed/fragment-1.5.1/res/animator/fragment_close_enter.xml" qualifiers="" type="animator"/><file name="fragment_open_exit" path="/home/<USER>/.gradle/caches/8.13/transforms/3f662f34ce46601d59f8125b7629f881/transformed/fragment-1.5.1/res/animator/fragment_open_exit.xml" qualifiers="" type="animator"/><file name="fragment_close_exit" path="/home/<USER>/.gradle/caches/8.13/transforms/3f662f34ce46601d59f8125b7629f881/transformed/fragment-1.5.1/res/animator/fragment_close_exit.xml" qualifiers="" type="animator"/><file name="fragment_fast_out_extra_slow_in" path="/home/<USER>/.gradle/caches/8.13/transforms/3f662f34ce46601d59f8125b7629f881/transformed/fragment-1.5.1/res/anim/fragment_fast_out_extra_slow_in.xml" qualifiers="" type="anim"/><file name="fragment_fast_out_extra_slow_in" path="/home/<USER>/.gradle/caches/8.13/transforms/3f662f34ce46601d59f8125b7629f881/transformed/fragment-1.5.1/res/anim-v21/fragment_fast_out_extra_slow_in.xml" qualifiers="v21" type="anim"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/AndroidStudioProjects/OneClickAwayCompose/domain/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/AndroidStudioProjects/OneClickAwayCompose/domain/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/AndroidStudioProjects/OneClickAwayCompose/domain/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/AndroidStudioProjects/OneClickAwayCompose/domain/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/AndroidStudioProjects/OneClickAwayCompose/domain/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/AndroidStudioProjects/OneClickAwayCompose/domain/build/generated/res/resValues/release"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="FontFamilyFont">
        
        <attr name="fontStyle">
            <enum name="normal" value="0"/>
            <enum name="italic" value="1"/>
        </attr>
        
        <attr format="reference" name="font"/>
        
        <attr format="integer" name="fontWeight"/>
        
        <attr format="string" name="fontVariationSettings"/>
        
        <attr format="integer" name="ttcIndex"/>
        
        <attr name="android:fontStyle"/>
        <attr name="android:font"/>
        <attr name="android:fontWeight"/>
        <attr name="android:fontVariationSettings"/>
        <attr name="android:ttcIndex"/>
    </declare-styleable><declare-styleable name="GradientColorItem">
        
        <attr name="android:offset"/>
        
        <attr name="android:color"/>
    </declare-styleable><declare-styleable name="FontFamily">
        
        <attr format="string" name="fontProviderAuthority"/>
        
        <attr format="string" name="fontProviderPackage"/>
        
        <attr format="string" name="fontProviderQuery"/>
        
        <attr format="reference" name="fontProviderCerts"/>
        
        <attr name="fontProviderFetchStrategy">
            
            <enum name="blocking" value="0"/>
            
            <enum name="async" value="1"/>
        </attr>
        
        <attr format="integer" name="fontProviderFetchTimeout">
            
            <enum name="forever" value="-1"/>
        </attr>
        
        <attr format="string" name="fontProviderSystemFontFamily"/>
    </declare-styleable><declare-styleable name="Capability">
        
        <attr format="reference" name="queryPatterns"/>
        
        <attr format="boolean" name="shortcutMatchRequired"/>
    </declare-styleable><declare-styleable name="GradientColor">
        
        <attr name="android:startColor"/>
        
        <attr name="android:centerColor"/>
        
        <attr name="android:endColor"/>
        
        <attr name="android:type"/>

        
        
        <attr name="android:gradientRadius"/>

        
        
        <attr name="android:centerX"/>
        
        <attr name="android:centerY"/>

        
        
        <attr name="android:startX"/>
        
        <attr name="android:startY"/>
        
        <attr name="android:endX"/>
        
        <attr name="android:endY"/>

        
        <attr name="android:tileMode"/>
    </declare-styleable><declare-styleable name="ColorStateListItem">
        
        <attr name="android:color"/>
        
        <attr format="float" name="alpha"/>
        <attr name="android:alpha"/>
        
        <attr format="float" name="lStar"/>
        <attr name="android:lStar"/>
    </declare-styleable><declare-styleable name="Fragment">
        <attr name="android:name"/>
        <attr name="android:id"/>
        <attr name="android:tag"/>
    </declare-styleable><declare-styleable name="FragmentContainerView">
        <attr name="android:name"/>
        <attr name="android:tag"/>
    </declare-styleable></configuration></mergedItems></merger>