<dependencies>
  <compile
      roots="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1@jar,com.google.dagger:hilt-android:2.54@aar,androidx.fragment:fragment:1.5.1@aar,androidx.activity:activity:1.5.1@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1@jar,androidx.core:core-ktx:1.2.0@aar,androidx.loader:loader:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.8.0@aar,androidx.core:core:1.8.0@aar,androidx.annotation:annotation-experimental:1.3.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar,androidx.savedstate:savedstate:1.2.0@aar,org.jetbrains.kotlin:kotlin-stdlib:2.2.0@jar,org.jetbrains:annotations:23.0.0@jar,com.google.dagger:hilt-core:2.54@jar,com.google.dagger:dagger:2.54@jar,jakarta.inject:jakarta.inject-api:2.0.1@jar,javax.inject:javax.inject:1@jar,org.jspecify:jspecify:1.0.0@jar,com.google.dagger:dagger-lint-aar:2.54@aar,com.google.code.findbugs:jsr305:3.0.2@jar,androidx.lifecycle:lifecycle-runtime:2.5.1@aar,androidx.lifecycle:lifecycle-livedata:2.0.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar,androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar,androidx.lifecycle:lifecycle-common:2.5.1@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.arch.core:core-runtime:2.1.0@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.annotation:annotation:1.3.0@jar">
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="com.google.dagger:hilt-android:2.54@aar"
        simpleName="com.google.dagger:hilt-android"/>
    <dependency
        name="androidx.fragment:fragment:1.5.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.5.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.core:core-ktx:1.2.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.8.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.0@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.2.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.dagger:hilt-core:2.54@jar"
        simpleName="com.google.dagger:hilt-core"/>
    <dependency
        name="com.google.dagger:dagger:2.54@jar"
        simpleName="com.google.dagger:dagger"/>
    <dependency
        name="jakarta.inject:jakarta.inject-api:2.0.1@jar"
        simpleName="jakarta.inject:jakarta.inject-api"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="com.google.dagger:dagger-lint-aar:2.54@aar"
        simpleName="com.google.dagger:dagger-lint-aar"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.5.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.1.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation:1.3.0@jar"
        simpleName="androidx.annotation:annotation"/>
  </compile>
  <package
      roots="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1@jar,com.google.dagger:hilt-android:2.54@aar,androidx.fragment:fragment:1.5.1@aar,androidx.activity:activity:1.5.1@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar,androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1@jar,androidx.core:core-ktx:1.2.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.8.0@aar,androidx.core:core:1.8.0@aar,androidx.annotation:annotation-experimental:1.3.1@aar,androidx.savedstate:savedstate:1.2.0@aar,org.jetbrains.kotlin:kotlin-stdlib:2.2.0@jar,org.jetbrains:annotations:23.0.0@jar,com.google.dagger:hilt-core:2.54@jar,com.google.dagger:dagger:2.54@jar,com.google.dagger:dagger-lint-aar:2.54@aar,com.google.code.findbugs:jsr305:3.0.2@jar,androidx.lifecycle:lifecycle-runtime:2.5.1@aar,androidx.lifecycle:lifecycle-livedata:2.0.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar,androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar,androidx.lifecycle:lifecycle-common:2.5.1@jar,androidx.tracing:tracing:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.arch.core:core-runtime:2.1.0@aar,androidx.arch.core:core-common:2.1.0@jar,androidx.concurrent:concurrent-futures:1.0.0@jar,androidx.annotation:annotation:1.3.0@jar,javax.inject:javax.inject:1@jar,jakarta.inject:jakarta.inject-api:2.0.1@jar,org.jspecify:jspecify:1.0.0@jar,com.google.guava:listenablefuture:1.0@jar">
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.10.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="com.google.dagger:hilt-android:2.54@aar"
        simpleName="com.google.dagger:hilt-android"/>
    <dependency
        name="androidx.fragment:fragment:1.5.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.5.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.core:core-ktx:1.2.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.8.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.3.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.0@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.2.0@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.dagger:hilt-core:2.54@jar"
        simpleName="com.google.dagger:hilt-core"/>
    <dependency
        name="com.google.dagger:dagger:2.54@jar"
        simpleName="com.google.dagger:dagger"/>
    <dependency
        name="com.google.dagger:dagger-lint-aar:2.54@aar"
        simpleName="com.google.dagger:dagger-lint-aar"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.5.1@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.1.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.1.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.0.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.annotation:annotation:1.3.0@jar"
        simpleName="androidx.annotation:annotation"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="jakarta.inject:jakarta.inject-api:2.0.1@jar"
        simpleName="jakarta.inject:jakarta.inject-api"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
  </package>
</dependencies>
