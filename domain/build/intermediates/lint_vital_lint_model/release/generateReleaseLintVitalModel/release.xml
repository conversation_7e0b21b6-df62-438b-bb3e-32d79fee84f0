<variant
    name="release"
    package="com.htueko.oneclickawaycompose.domain"
    minSdkVersion="24"
    targetSdkVersion="24"
    mergedManifest="build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml"
    manifestMergeReport="build/outputs/logs/manifest-merger-release-report.txt"
    proguardFiles="build/intermediates/default_proguard_files/global/proguard-android-optimize.txt-8.12.0:proguard-rules.pro"
    consumerProguardFiles="consumer-rules.pro"
    partialResultsDir="build/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src/main/AndroidManifest.xml"
        javaDirectories="src/main/java:src/release/java:src/main/kotlin:src/release/kotlin"
        resDirectories="src/main/res:src/release/res"
        assetsDirectories="src/main/assets:src/release/assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build/intermediates/javac/release/compileReleaseJavaWithJavac/classes:build/tmp/kotlin-classes/release:build/intermediates/compile_r_class_jar/release/generateReleaseRFile/R.jar"
      type="MAIN"
      applicationId="com.htueko.oneclickawaycompose.domain"
      generatedSourceFolders="build/generated/ap_generated_sources/release/out"
      generatedResourceFolders="build/generated/res/resValues/release"
      desugaredMethodsFiles="/home/<USER>/.gradle/caches/8.13/transforms/04d2e936ccda1075a1edc168ec12bb89/transformed/D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
