{"logs": [{"outputFile": "com.htueko.oneclickawaycompose.domain-release-12:/values-pl/values-pl.xml", "map": [{"source": "/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-pl/values-pl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}, {"outputFile": "com.htueko.oneclickawaycompose.domain-mergeReleaseResources-10:/values-pl/values-pl.xml", "map": [{"source": "/home/<USER>/.gradle/caches/8.13/transforms/ceed12bedf22426b02a7dca4454f7310/transformed/core-1.8.0/res/values-pl/values-pl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}}]}]}