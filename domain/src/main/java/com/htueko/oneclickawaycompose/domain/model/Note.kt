package com.htueko.oneclickawaycompose.domain.model

/**
 * Domain model representing a Note entity.
 * 
 * WHY: This is a pure domain model without any framework dependencies,
 * following Clean Architecture principles where the domain layer should be
 * independent of external frameworks and libraries.
 * 
 * Reference: https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html
 */
data class Note(
    val id: Long = 0L,
    val text: String,
    val createdAt: Long = System.currentTimeMillis()
)
