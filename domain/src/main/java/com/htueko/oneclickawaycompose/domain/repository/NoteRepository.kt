package com.htueko.oneclickawaycompose.domain.repository

import com.htueko.oneclickawaycompose.domain.model.Note
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for Note operations.
 * 
 * WHY: This interface defines the contract for data operations without exposing
 * implementation details. It allows the domain layer to remain independent of
 * the data layer, enabling easy testing and swapping of data sources.
 * 
 * WHY: Using Flow for getAllNotes() enables reactive programming, allowing the UI
 * to automatically update when data changes in the database.
 * 
 * WHY: Using Result wrapper provides explicit error handling and makes the API
 * more predictable for consumers.
 * 
 * References:
 * - Clean Architecture: https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html
 * - Kotlin Flow: https://kotlinlang.org/docs/flow.html
 * - Result pattern: https://github.com/Kotlin/KEEP/blob/master/proposals/stdlib/result.md
 */
interface NoteRepository {
    
    /**
     * Observes all notes from the data source.
     * @return Flow of list of notes that updates automatically when data changes
     */
    fun getAllNotes(): Flow<List<Note>>
    
    /**
     * Adds a new note to the data source.
     * @param note The note to add
     * @return Result indicating success or failure
     */
    suspend fun addNote(note: Note): Result<Unit>
    
    /**
     * Deletes a note from the data source.
     * @param noteId The ID of the note to delete
     * @return Result indicating success or failure
     */
    suspend fun deleteNote(noteId: Long): Result<Unit>
    
    /**
     * Updates an existing note in the data source.
     * @param note The note to update
     * @return Result indicating success or failure
     */
    suspend fun updateNote(note: Note): Result<Unit>
}
