package com.htueko.oneclickawaycompose.domain.usecase

import com.htueko.oneclickawaycompose.domain.model.Note
import com.htueko.oneclickawaycompose.domain.repository.NoteRepository
import javax.inject.Inject

/**
 * Use case for adding a new note.
 * 
 * WHY: This use case encapsulates the business logic for creating a note,
 * including validation and any business rules that might apply.
 * 
 * WHY: Using suspend function allows this operation to be performed
 * asynchronously without blocking the calling thread.
 * 
 * WHY: Input validation at the domain layer ensures data integrity
 * regardless of which presentation layer calls this use case.
 * 
 * References:
 * - Clean Architecture: https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html
 * - Kotlin Coroutines: https://kotlinlang.org/docs/coroutines-overview.html
 */
class AddNoteUseCase @Inject constructor(
    private val noteRepository: NoteRepository
) {
    
    /**
     * Executes the use case to add a new note.
     * @param text The text content of the note
     * @return Result indicating success or failure with error details
     */
    suspend operator fun invoke(text: String): Result<Unit> {
        // WHY: Validate input at the domain layer to ensure business rules
        // are enforced regardless of the calling context
        if (text.isBlank()) {
            return Result.failure(IllegalArgumentException("Note text cannot be empty"))
        }
        
        val note = Note(
            text = text.trim(),
            createdAt = System.currentTimeMillis()
        )
        
        return noteRepository.addNote(note)
    }
}
