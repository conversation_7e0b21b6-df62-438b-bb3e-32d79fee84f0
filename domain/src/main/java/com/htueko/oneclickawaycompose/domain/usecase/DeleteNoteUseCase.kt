package com.htueko.oneclickawaycompose.domain.usecase

import com.htueko.oneclickawaycompose.domain.repository.NoteRepository
import javax.inject.Inject

/**
 * Use case for deleting a note.
 * 
 * WHY: Encapsulates the business logic for note deletion, including
 * any validation or business rules that should be applied.
 * 
 * WHY: Using Result type provides explicit error handling and makes
 * the API contract clear to consumers.
 * 
 * References:
 * - Clean Architecture: https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html
 * - Result pattern: https://github.com/Kotlin/KEEP/blob/master/proposals/stdlib/result.md
 */
class DeleteNoteUseCase @Inject constructor(
    private val noteRepository: NoteRepository
) {
    
    /**
     * Executes the use case to delete a note.
     * @param noteId The ID of the note to delete
     * @return Result indicating success or failure
     */
    suspend operator fun invoke(noteId: Long): Result<Unit> {
        // WHY: Validate input to prevent invalid operations
        if (noteId <= 0) {
            return Result.failure(IllegalArgumentException("Invalid note ID"))
        }
        
        return noteRepository.deleteNote(noteId)
    }
}
