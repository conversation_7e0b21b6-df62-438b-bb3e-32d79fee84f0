package com.htueko.oneclickawaycompose.domain.usecase

import com.htueko.oneclickawaycompose.domain.model.Note
import com.htueko.oneclickawaycompose.domain.repository.NoteRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Use case for retrieving all notes.
 * 
 * WHY: Use cases encapsulate business logic and provide a clear API for the
 * presentation layer. They follow the Single Responsibility Principle by
 * handling only one specific business operation.
 * 
 * WHY: Using dependency injection makes this class testable and follows
 * the Dependency Inversion Principle from SOLID principles.
 * 
 * WHY: Returning Flow allows the UI to reactively update when notes change,
 * providing a better user experience.
 * 
 * References:
 * - Clean Architecture: https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html
 * - SOLID Principles: https://en.wikipedia.org/wiki/SOLID
 * - Kotlin Flow: https://kotlinlang.org/docs/flow.html
 */
class GetAllNotesUseCase @Inject constructor(
    private val noteRepository: NoteRepository
) {
    
    /**
     * Executes the use case to get all notes.
     * @return Flow of list of notes
     */
    operator fun invoke(): Flow<List<Note>> {
        return noteRepository.getAllNotes()
    }
}
