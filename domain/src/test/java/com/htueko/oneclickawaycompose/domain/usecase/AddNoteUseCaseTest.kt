package com.htueko.oneclickawaycompose.domain.usecase

import com.htueko.oneclickawaycompose.domain.model.Note
import com.htueko.oneclickawaycompose.domain.repository.NoteRepository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for AddNoteUseCase.
 * 
 * WHY: Unit tests ensure business logic works correctly in isolation,
 * following the Test Pyramid principle where unit tests form the base.
 * 
 * WHY: Using MockK allows us to isolate the use case from its dependencies
 * and test only the business logic without external dependencies.
 * 
 * WHY: runTest provides proper coroutine testing support and handles
 * test dispatchers automatically.
 * 
 * References:
 * - Test Pyramid: https://martinfowler.com/articles/practical-test-pyramid.html
 * - MockK: https://mockk.io/
 * - Coroutine Testing: https://kotlinlang.org/docs/coroutines-testing.html
 */
class AddNoteUseCaseTest {
    
    private lateinit var noteRepository: NoteRepository
    private lateinit var addNoteUseCase: AddNoteUseCase
    
    @Before
    fun setup() {
        noteRepository = mockk()
        addNoteUseCase = AddNoteUseCase(noteRepository)
    }
    
    @Test
    fun `invoke with valid text should call repository addNote`() = runTest {
        // Given
        val noteText = "Test note"
        val expectedNote = Note(text = noteText.trim(), createdAt = 0L)
        coEvery { noteRepository.addNote(any()) } returns Result.success(Unit)
        
        // When
        val result = addNoteUseCase(noteText)
        
        // Then
        assertTrue(result.isSuccess)
        coVerify { noteRepository.addNote(match { it.text == noteText.trim() }) }
    }
    
    @Test
    fun `invoke with blank text should return failure`() = runTest {
        // Given
        val blankText = "   "
        
        // When
        val result = addNoteUseCase(blankText)
        
        // Then
        assertTrue(result.isFailure)
        assertEquals("Note text cannot be empty", result.exceptionOrNull()?.message)
        coVerify(exactly = 0) { noteRepository.addNote(any()) }
    }
    
    @Test
    fun `invoke with empty text should return failure`() = runTest {
        // Given
        val emptyText = ""
        
        // When
        val result = addNoteUseCase(emptyText)
        
        // Then
        assertTrue(result.isFailure)
        assertEquals("Note text cannot be empty", result.exceptionOrNull()?.message)
        coVerify(exactly = 0) { noteRepository.addNote(any()) }
    }
    
    @Test
    fun `invoke should trim whitespace from text`() = runTest {
        // Given
        val textWithWhitespace = "  Test note  "
        val expectedTrimmedText = "Test note"
        coEvery { noteRepository.addNote(any()) } returns Result.success(Unit)
        
        // When
        addNoteUseCase(textWithWhitespace)
        
        // Then
        coVerify { noteRepository.addNote(match { it.text == expectedTrimmedText }) }
    }
    
    @Test
    fun `invoke should propagate repository failure`() = runTest {
        // Given
        val noteText = "Test note"
        val expectedException = RuntimeException("Database error")
        coEvery { noteRepository.addNote(any()) } returns Result.failure(expectedException)
        
        // When
        val result = addNoteUseCase(noteText)
        
        // Then
        assertTrue(result.isFailure)
        assertEquals(expectedException, result.exceptionOrNull())
    }
}
