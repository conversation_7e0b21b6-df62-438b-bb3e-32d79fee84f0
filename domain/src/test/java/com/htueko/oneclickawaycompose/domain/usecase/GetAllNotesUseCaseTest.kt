package com.htueko.oneclickawaycompose.domain.usecase

import com.htueko.oneclickawaycompose.domain.model.Note
import com.htueko.oneclickawaycompose.domain.repository.NoteRepository
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for GetAllNotesUseCase.
 * 
 * WHY: Testing the use case ensures that it correctly delegates to the
 * repository and doesn't introduce any unintended business logic.
 * 
 * WHY: Flow testing verifies that reactive data streams work correctly
 * and emit the expected values.
 * 
 * References:
 * - Flow Testing: https://kotlinlang.org/docs/flow.html#testing
 * - MockK: https://mockk.io/
 */
class GetAllNotesUseCaseTest {
    
    private lateinit var noteRepository: NoteRepository
    private lateinit var getAllNotesUseCase: GetAllNotesUseCase
    
    @Before
    fun setup() {
        noteRepository = mockk()
        getAllNotesUseCase = GetAllNotesUseCase(noteRepository)
    }
    
    @Test
    fun `invoke should return flow from repository`() = runTest {
        // Given
        val expectedNotes = listOf(
            Note(id = 1, text = "Note 1", createdAt = 1000L),
            Note(id = 2, text = "Note 2", createdAt = 2000L)
        )
        every { noteRepository.getAllNotes() } returns flowOf(expectedNotes)
        
        // When
        val result = getAllNotesUseCase().toList()
        
        // Then
        assertEquals(1, result.size)
        assertEquals(expectedNotes, result.first())
        verify { noteRepository.getAllNotes() }
    }
    
    @Test
    fun `invoke should return empty list when repository returns empty flow`() = runTest {
        // Given
        every { noteRepository.getAllNotes() } returns flowOf(emptyList())
        
        // When
        val result = getAllNotesUseCase().toList()
        
        // Then
        assertEquals(1, result.size)
        assertEquals(emptyList<Note>(), result.first())
        verify { noteRepository.getAllNotes() }
    }
}
